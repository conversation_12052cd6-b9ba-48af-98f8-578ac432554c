<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Binary Converter Tools – Convert, Encode & Decode Binary Online</title>
  <meta name="description" content="Convert, encode, and decode binary data using our free binary tools. Easily switch between binary, decimal, hexadecimal, and text formats — perfect for developers, students, and tech enthusiasts." />
  <meta name="keywords" content="binary tools, binary converter, binary to text, text to binary, binary to decimal, decimal to binary, hex to binary, binary encoder, binary decoder, online tools, free tools" />
  <link rel="canonical" href="https://www.webtoolskit.org/p/binary-converter_88.html" />

  <!-- Page-specific Open Graph Meta Tags -->
  <meta property="og:url" content="https://www.webtoolskit.org/p/binary-converter_88.html" />
  <meta property="og:title" content="Binary Converter Tools - Free Online Binary Utilities" />
  <meta property="og:description" content="Access a complete set of free online binary tools. Convert between binary, text, decimal, and hexadecimal, encode/decode data, and more with our professional binary utilities." />
  <meta property="og:image" content="https://www.webtoolskit.org/images/binary-og.jpg" />

  <!-- Page-specific Twitter Card Meta Tags -->
  <meta name="twitter:url" content="https://www.webtoolskit.org/p/binary-converter_88.html" />
  <meta name="twitter:title" content="Binary Converter Tools - Free Online Binary Utilities" />
  <meta name="twitter:description" content="Access a complete set of free online binary tools. Convert between binary, text, decimal, and hexadecimal, encode/decode data, and more with our professional binary utilities." />
  <meta name="twitter:image" content="https://www.webtoolskit.org/images/binary-og.jpg" />

  <!-- Enhanced Schema.org structured data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": ["WebPage", "CollectionPage"],
    "name": "Binary Converter Tools – Convert, Encode & Decode Binary Online",
    "description": "Convert, encode, and decode binary data using our free binary tools. Easily switch between binary, decimal, hexadecimal, and text formats — perfect for developers, students, and tech enthusiasts.",
    "url": "https://www.webtoolskit.org/p/binary-converter_88.html",
    "isAccessibleForFree": true,
    "datePublished": "2025-06-15",
    "dateModified": "2025-06-19",
    "author": {
      "@type": "Organization",
      "name": "WebToolsKit",
      "url": "https://www.webtoolskit.org",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.webtoolskit.org/images/logo.png"
      }
    },
    "publisher": {
      "@type": "Organization",
      "name": "WebToolsKit",
      "url": "https://www.webtoolskit.org"
    },
    "mainEntity": {
      "@type": "ItemList",
      "name": "Binary Converter Tools Collection",
      "description": "Comprehensive collection of free online binary tools",
      "numberOfItems": 24,
      "itemListElement": [
        {
          "@type": "WebApplication",
          "position": 1,
          "name": "Binary to Text Converter",
          "description": "Convert binary code to readable text instantly.",
          "url": "https://www.webtoolskit.org/p/binary-to-text.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Binary to text conversion", "Instant decoding", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 2,
          "name": "Text to Binary Converter",
          "description": "Convert text to binary code for encoding and data processing.",
          "url": "https://www.webtoolskit.org/p/text-to-binary.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Text to binary conversion", "Encoding", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 3,
          "name": "Binary to Decimal Converter",
          "description": "Convert binary numbers to decimal format easily.",
          "url": "https://www.webtoolskit.org/p/binary-to-decimal.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Binary to decimal conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 4,
          "name": "Decimal to Binary Converter",
          "description": "Convert decimal numbers to binary code instantly.",
          "url": "https://www.webtoolskit.org/p/decimal-to-binary.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Decimal to binary conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 5,
          "name": "Hex to Binary Converter",
          "description": "Convert hexadecimal values to binary format.",
          "url": "https://www.webtoolskit.org/p/hex-to-binary.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Hex to binary conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 6,
          "name": "Binary Encoder/Decoder",
          "description": "Encode or decode binary data for various applications.",
          "url": "https://www.webtoolskit.org/p/binary-encoder-decoder.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Binary encoding", "Binary decoding", "Data processing"]
        },
        {
          "@type": "WebApplication",
          "position": 7,
          "name": "Binary to HEX Converter",
          "description": "Convert binary code to hexadecimal format for easier reading and debugging.",
          "url": "https://www.webtoolskit.org/p/binary-to-hex.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Binary to HEX conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 8,
          "name": "Text to ASCII Converter",
          "description": "Convert text characters to ASCII codes for programming and data processing.",
          "url": "https://www.webtoolskit.org/p/text-to-ascii.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Text to ASCII conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 9,
          "name": "ASCII to Text Converter",
          "description": "Convert ASCII codes back to readable text characters for data decoding.",
          "url": "https://www.webtoolskit.org/p/ascii-to-text.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["ASCII to text conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 10,
          "name": "ASCII to Binary Converter",
          "description": "Convert ASCII codes to binary representation for low-level programming.",
          "url": "https://www.webtoolskit.org/p/ascii-to-binary.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["ASCII to binary conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 11,
          "name": "Binary to ASCII Converter",
          "description": "Convert binary code to ASCII values for character encoding analysis.",
          "url": "https://www.webtoolskit.org/p/binary-to-ascii.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Binary to ASCII conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 12,
          "name": "HEX to Decimal Converter",
          "description": "Convert hexadecimal numbers to decimal format for mathematical operations.",
          "url": "https://www.webtoolskit.org/p/hex-to-decimal.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["HEX to decimal conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 13,
          "name": "Decimal to HEX Converter",
          "description": "Convert decimal numbers to hexadecimal format for programming applications.",
          "url": "https://www.webtoolskit.org/p/decimal-to-hex.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Decimal to HEX conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 14,
          "name": "Octal to Binary Converter",
          "description": "Convert octal numbers to binary representation for system programming.",
          "url": "https://www.webtoolskit.org/p/octal-to-binary.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Octal to binary conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 15,
          "name": "Binary to Octal Converter",
          "description": "Convert binary code to octal format for easier reading and debugging.",
          "url": "https://www.webtoolskit.org/p/binary-to-octal.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Binary to octal conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 16,
          "name": "Octal to Decimal Converter",
          "description": "Convert octal numbers to decimal format for mathematical calculations.",
          "url": "https://www.webtoolskit.org/p/octal-to-decimal.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Octal to decimal conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 17,
          "name": "Decimal to Octal Converter",
          "description": "Convert decimal numbers to octal representation for system programming.",
          "url": "https://www.webtoolskit.org/p/decimal-to-octal.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Decimal to octal conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 18,
          "name": "HEX to Octal Converter",
          "description": "Convert hexadecimal values to octal format for number system conversions.",
          "url": "https://www.webtoolskit.org/p/hex-to-octal.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["HEX to octal conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 19,
          "name": "Octal to HEX Converter",
          "description": "Convert octal numbers to hexadecimal format for programming tasks.",
          "url": "https://www.webtoolskit.org/p/octal-to-hex.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Octal to HEX conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 20,
          "name": "Text to Octal Converter",
          "description": "Convert text characters to octal representation for data encoding.",
          "url": "https://www.webtoolskit.org/p/text-to-octal.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Text to octal conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 21,
          "name": "Octal to Text Converter",
          "description": "Convert octal codes back to readable text for data decoding.",
          "url": "https://www.webtoolskit.org/p/octal-to-text.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Octal to text conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 22,
          "name": "Text to HEX Converter",
          "description": "Convert text characters to hexadecimal representation for data encoding.",
          "url": "https://www.webtoolskit.org/p/text-to-hex.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Text to HEX conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 23,
          "name": "HEX to Text Converter",
          "description": "Convert hexadecimal codes back to readable text for data analysis.",
          "url": "https://www.webtoolskit.org/p/hex-to-text.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["HEX to text conversion", "Instant calculation", "Bulk processing"]
        },
        {
          "@type": "WebApplication",
          "position": 24,
          "name": "Text to Decimal Converter",
          "description": "Convert text characters to decimal ASCII values for programming tasks.",
          "url": "https://www.webtoolskit.org/p/text-to-decimal.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Text to decimal conversion", "Instant calculation", "Bulk processing"]
        }
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {"@type": "ListItem", "position": 1, "name": "Home", "item": "https://www.webtoolskit.org"},
        {"@type": "ListItem", "position": 2, "name": "Binary Converter Tools"}
      ]
    }
  }
  </script>
  <style>
    /* Duplicate CSS variables and base styles removed - inherit from main template */

    /* Page-specific styles only */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 8px 16px;
    }

    .page-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .page-title {
      font-size: 36px;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 10px;
      line-height: 1.2;
    }

    .page-description {
      font-size: 1.1rem;
      color: var(--text-color-light);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.5;
    }

    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
      justify-items: center;
    }

    .tool-card {
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 18px;
      text-align: center;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }

    .tool-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      opacity: 0;
      transition: opacity 0.4s ease;
    }

    .tool-card::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .tool-card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 12px 35px rgba(0,0,0,0.2);
      border-color: var(--primary-color);
    }

    .tool-card:hover::before {
      opacity: 1;
    }

    .tool-card:hover::after {
      left: 100%;
    }

    .tool-icon {
      width: 56px;
      height: 56px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      font-size: 22px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .tool-icon::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .tool-card:hover .tool-icon {
      transform: scale(1.1) rotate(5deg);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .tool-card:hover .tool-icon::before {
      opacity: 1;
    }

    /* Distinctive Icon Colors */
    .icon-binary-to-text { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; }
    .icon-text-to-binary { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-binary-to-decimal { background: linear-gradient(135deg, #EC4899, #DB2777); color: white; }
    .icon-decimal-to-binary { background: linear-gradient(135deg, #10B981, #059669); color: white; }
    .icon-hex-to-binary { background: linear-gradient(135deg, #F59E0B, #D97706); color: white; }
    .icon-binary-encoder { background: linear-gradient(135deg, #6366F1, #4F46E5); color: white; }
    .icon-binary-to-hex { background: linear-gradient(135deg, #F97316, #EA580C); color: white; }
    .icon-text-to-ascii { background: linear-gradient(135deg, #0EA5E9, #0284C7); color: white; }
    .icon-ascii-to-text { background: linear-gradient(135deg, #14B8A6, #0D9488); color: white; }
    .icon-ascii-to-binary { background: linear-gradient(135deg, #4F46E5, #4338CA); color: white; }
    .icon-binary-to-ascii { background: linear-gradient(135deg, #8B5CF6, #7C3AED); color: white; }
    .icon-hex-to-decimal { background: linear-gradient(135deg, #EF4444, #DC2626); color: white; }
    .icon-decimal-to-hex { background: linear-gradient(135deg, #D97706, #B45309); color: white; }
    .icon-octal-to-binary { background: linear-gradient(135deg, #059669, #047857); color: white; }
    .icon-binary-to-octal { background: linear-gradient(135deg, #1d4ed8, #1e40af); color: white; }
    .icon-octal-to-decimal { background: linear-gradient(135deg, #DB2777, #BE185D); color: white; }
    .icon-decimal-to-octal { background: linear-gradient(135deg, #4F46E5, #4338CA); color: white; }
    .icon-hex-to-octal { background: linear-gradient(135deg, #6D28D9, #5B21B6); color: white; }
    .icon-octal-to-hex { background: linear-gradient(135deg, #C026D3, #A21CAF); color: white; }
    .icon-text-to-octal { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; }
    .icon-octal-to-text { background: linear-gradient(135deg, #0EA5E9, #0284C7); color: white; }
    .icon-text-to-hex { background: linear-gradient(135deg, #F59E0B, #D97706); color: white; }
    .icon-hex-to-text { background: linear-gradient(135deg, #10B981, #059669); color: white; }
    .icon-text-to-decimal { background: linear-gradient(135deg, #6366F1, #4F46E5); color: white; }


    .tool-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 8px;
    }

    .tool-description {
      color: var(--text-color-light);
      font-size: 14px;
      margin-bottom: 12px;
      line-height: 1.4;
    }

    .tool-link {
      display: inline-block;
      background: var(--primary-color);
      color: #ffffff !important;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid var(--primary-color);
      box-shadow: 0 2px 8px rgba(0, 71, 171, 0.2);
      position: relative;
      overflow: hidden;
    }

    .tool-link::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    .tool-link:hover {
      background: #003d96;
      border-color: #003d96;
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
      color: #ffffff !important;
    }

    .tool-link:hover::before {
      left: 100%;
    }

    [data-theme="dark"] .tool-link {
      background: #60a5fa;
      border-color: #60a5fa;
      color: #ffffff !important;
      box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
    }

    [data-theme="dark"] .tool-link:hover {
      background: #3b82f6;
      border-color: #3b82f6;
      box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
      color: #ffffff !important;
    }
    
    /* Mobile Optimization with Scrollable Icons */
    @media (max-width: 768px) {
      .container { padding: 6px 12px; }
      .page-header { margin-bottom: 16px; }
      .page-title { font-size: 28px; margin-bottom: 8px; }
      .page-description { font-size: 1rem; padding: 0 8px; }

      /* Mobile single column layout */
      .tools-grid {
        display: block;
        overflow-x: unset;
        padding: 8px 0;
      }
      .tool-card {
        width: 100%;
        margin: 0 0 12px 0;
        border-radius: 14px;
        padding: 16px;
        min-height: 80px;
        box-sizing: border-box;
        /* Remove all transitions except for description for stability */
      }
      .tool-card .tool-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 8px;
        font-size: 20px;
      }
      .tool-card .tool-title,
      .tool-card .tool-link {
        opacity: 1;
        transform: none;
        pointer-events: auto;
      }
      .tool-card .tool-description {
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        margin: 0;
        transition: opacity 0.3s, max-height 0.3s;
        will-change: opacity, max-height;
        display: block;
      }
      .tool-card.show-description .tool-description {
        opacity: 1;
        max-height: 100px;
        margin-bottom: 10px;
      }
    }
    @media (max-width: 480px) {
      .tool-card { min-width: 200px; max-width: 95vw; padding: 10px; }
      .tool-card .tool-icon { width: 40px; height: 40px; font-size: 18px; }
    }
    @media (max-width: 320px) {
      .tool-card { min-width: 140px; padding: 6px; }
      .tool-card .tool-icon { width: 32px; height: 32px; font-size: 15px; }
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="page-header">
      <h1 id="main-title" class="page-title">Binary Converter Tools – Convert, Encode &amp; Decode Binary Online</h1>
      <p class="page-description">Convert, encode, and decode binary data using our free binary tools. Easily switch between binary, decimal, hexadecimal, and text formats — perfect for developers, students, and tech enthusiasts.</p>
    </header>
    <main id="main-content" role="main" aria-labelledby="main-title">
      <section class="tools-section" aria-labelledby="tools-section-title">
        <h2 id="tools-section-title" class="sr-only">Available Binary Tools</h2>
        <div class="tools-grid" role="list">
        <!-- Binary to Text -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-binary-to-text" aria-hidden="true">
            <i class="fas fa-font"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Binary to Text</h3>
            <p class="tool-description">Convert binary code to readable text instantly.</p>
            <a class="tool-link" href="/p/binary-to-text.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Text to Binary -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-text-to-binary" aria-hidden="true">
            <i class="fas fa-link"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Text to Binary</h3>
            <p class="tool-description">Convert text to binary code for encoding and data processing.</p>
            <a class="tool-link" href="/p/text-to-binary.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Binary to Decimal -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-binary-to-decimal" aria-hidden="true">
            <i class="fas fa-calculator"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Binary to Decimal</h3>
            <p class="tool-description">Convert binary numbers to decimal format easily.</p>
            <a class="tool-link" href="/p/binary-to-decimal.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Decimal to Binary -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-decimal-to-binary" aria-hidden="true">
            <i class="fas fa-sort-numeric-up"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Decimal to Binary</h3>
            <p class="tool-description">Convert decimal numbers to binary code instantly.</p>
            <a class="tool-link" href="/p/decimal-to-binary.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Hex to Binary -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-hex-to-binary" aria-hidden="true">
            <i class="fas fa-hashtag"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Hex to Binary</h3>
            <p class="tool-description">Convert hexadecimal values to binary format.</p>
            <a class="tool-link" href="/p/hex-to-binary.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Binary Encoder/Decoder -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-binary-encoder" aria-hidden="true">
            <i class="fas fa-random"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Binary Encoder/Decoder</h3>
            <p class="tool-description">Encode or decode binary data for various applications.</p>
            <a class="tool-link" href="/p/binary-encoder-decoder.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Binary to HEX -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-binary-to-hex" aria-hidden="true">
            <i class="fas fa-exchange-alt"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Binary to HEX</h3>
            <p class="tool-description">Convert binary code to hexadecimal format for easier reading and debugging.</p>
            <a class="tool-link" href="/p/binary-to-hex.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Text to ASCII -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-text-to-ascii" aria-hidden="true">
            <i class="fas fa-keyboard"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Text to ASCII</h3>
            <p class="tool-description">Convert text characters to ASCII codes for programming and data processing.</p>
            <a class="tool-link" href="/p/text-to-ascii.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- ASCII to Text -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-ascii-to-text" aria-hidden="true">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">ASCII to Text</h3>
            <p class="tool-description">Convert ASCII codes back to readable text characters for data decoding.</p>
            <a class="tool-link" href="/p/ascii-to-text.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- ASCII to Binary -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-ascii-to-binary" aria-hidden="true">
            <i class="fas fa-code"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">ASCII to Binary</h3>
            <p class="tool-description">Convert ASCII codes to binary representation for low-level programming.</p>
            <a class="tool-link" href="/p/ascii-to-binary.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Binary to ASCII -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-binary-to-ascii" aria-hidden="true">
            <i class="fas fa-font"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Binary to ASCII</h3>
            <p class="tool-description">Convert binary code to ASCII values for character encoding analysis.</p>
            <a class="tool-link" href="/p/binary-to-ascii.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- HEX to Decimal -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-hex-to-decimal" aria-hidden="true">
            <i class="fas fa-calculator"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">HEX to Decimal</h3>
            <p class="tool-description">Convert hexadecimal numbers to decimal format for mathematical operations.</p>
            <a class="tool-link" href="/p/hex-to-decimal.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Decimal to HEX -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-decimal-to-hex" aria-hidden="true">
            <i class="fas fa-exchange-alt"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Decimal to HEX</h3>
            <p class="tool-description">Convert decimal numbers to hexadecimal format for programming applications.</p>
            <a class="tool-link" href="/p/decimal-to-hex.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Octal to Binary -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-octal-to-binary" aria-hidden="true">
            <i class="fas fa-code-branch"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Octal to Binary</h3>
            <p class="tool-description">Convert octal numbers to binary representation for system programming.</p>
            <a class="tool-link" href="/p/octal-to-binary.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Binary to Octal -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-binary-to-octal" aria-hidden="true">
            <i class="fas fa-code-branch"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Binary to Octal</h3>
            <p class="tool-description">Convert binary code to octal format for easier reading and debugging.</p>
            <a class="tool-link" href="/p/binary-to-octal.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Octal to Decimal -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-octal-to-decimal" aria-hidden="true">
            <i class="fas fa-calculator"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Octal to Decimal</h3>
            <p class="tool-description">Convert octal numbers to decimal format for mathematical calculations.</p>
            <a class="tool-link" href="/p/octal-to-decimal.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Decimal to Octal -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-decimal-to-octal" aria-hidden="true">
            <i class="fas fa-code-branch"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Decimal to Octal</h3>
            <p class="tool-description">Convert decimal numbers to octal representation for system programming.</p>
            <a class="tool-link" href="/p/decimal-to-octal.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- HEX to Octal -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-hex-to-octal" aria-hidden="true">
            <i class="fas fa-hashtag"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">HEX to Octal</h3>
            <p class="tool-description">Convert hexadecimal values to octal format for number system conversions.</p>
            <a class="tool-link" href="/p/hex-to-octal.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Octal to HEX -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-octal-to-hex" aria-hidden="true">
            <i class="fas fa-exchange-alt"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Octal to HEX</h3>
            <p class="tool-description">Convert octal numbers to hexadecimal format for programming tasks.</p>
            <a class="tool-link" href="/p/octal-to-hex.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Text to Octal -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-text-to-octal" aria-hidden="true">
            <i class="fas fa-keyboard"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Text to Octal</h3>
            <p class="tool-description">Convert text characters to octal representation for data encoding.</p>
            <a class="tool-link" href="/p/text-to-octal.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Octal to Text -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-octal-to-text" aria-hidden="true">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Octal to Text</h3>
            <p class="tool-description">Convert octal codes back to readable text for data decoding.</p>
            <a class="tool-link" href="/p/octal-to-text.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Text to HEX -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-text-to-hex" aria-hidden="true">
            <i class="fas fa-keyboard"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Text to HEX</h3>
            <p class="tool-description">Convert text characters to hexadecimal representation for data encoding.</p>
            <a class="tool-link" href="/p/text-to-hex.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- HEX to Text -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-hex-to-text" aria-hidden="true">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">HEX to Text</h3>
            <p class="tool-description">Convert hexadecimal codes back to readable text for data analysis.</p>
            <a class="tool-link" href="/p/hex-to-text.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        <!-- Text to Decimal -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-text-to-decimal" aria-hidden="true">
            <i class="fas fa-calculator"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Text to Decimal</h3>
            <p class="tool-description">Convert text characters to decimal ASCII values for programming tasks.</p>
            <a class="tool-link" href="/p/text-to-decimal.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        </div>
      </section>
    </main>
  </div>
  <script>
    // --- SIMPLIFIED: Expand card on tap for mobile ---
    document.addEventListener('DOMContentLoaded', function() {
      if (window.innerWidth > 768) return;
      const toolCards = document.querySelectorAll('.tool-card');
      toolCards.forEach(card => {
        card.addEventListener('click', function() {
          toolCards.forEach(c => c.classList.remove('expanded'));
          this.classList.add('expanded');
        });
      });
    });

    // Mobile: Description toggles on click/tap, always works instantly
    document.addEventListener('DOMContentLoaded', function() {
      if (window.innerWidth > 768) return;
      const toolCards = document.querySelectorAll('.tool-card');
      // Prevent double event firing on touch devices
      let lastTouch = 0;
      toolCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.classList.add('show-description');
        });
        card.addEventListener('mouseleave', function() {
          this.classList.remove('show-description');
        });
        card.addEventListener('touchend', function(e) {
          e.preventDefault();
          lastTouch = Date.now();
          if (this.classList.contains('show-description')) {
            this.classList.remove('show-description');
          } else {
            toolCards.forEach(c => c.classList.remove('show-description'));
            this.classList.add('show-description');
          }
        }, { passive: false });
        card.addEventListener('click', function(e) {
          // Ignore click if just handled by touch
          if (Date.now() - lastTouch < 500) return;
          if (this.classList.contains('show-description')) {
            this.classList.remove('show-description');
          } else {
            toolCards.forEach(c => c.classList.remove('show-description'));
            this.classList.add('show-description');
          }
        });
      });
    });
  </script>
</body>
</html>