<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rotate Image - Free Online Image Rotation Tool</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Image Rotator - Rotate Images Online",
        "description": "Rotate images online by 90°, 180°, 270° or custom angles. Fix image orientation, rotate JPEG, PNG, GIF files instantly on phone or computer.",
        "url": "https://www.webtoolskit.org/p/rotate-image.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-21",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Image Rotation Tool",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "EditAction", "name": "Rotate Image 90 Degrees" },
            { "@type": "EditAction", "name": "Rotate Image 180 Degrees" },
            { "@type": "EditAction", "name": "Rotate Image Custom Angle" },
            { "@type": "DownloadAction", "name": "Download Rotated Image" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I rotate a photo on my phone or computer?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Upload your photo to our online rotation tool, then use the rotation buttons to rotate 90°, 180°, 270°, or enter a custom angle. Our tool works on any device - phone, tablet, or computer - directly in your browser."
          }
        },
        {
          "@type": "Question",
          "name": "Why won't my pictures rotate automatically?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Pictures may not auto-rotate due to missing or incorrect EXIF orientation data, browser limitations, or device settings. Our tool manually rotates images regardless of EXIF data, ensuring consistent results across all devices and platforms."
          }
        },
        {
          "@type": "Question",
          "name": "How do I fix the rotation of an image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Upload your image to our rotation tool and use the rotation buttons to correct the orientation. Use 'Rotate Right' (90°) or 'Rotate Left' (-90°) to fix sideways images, or '180°' for upside-down images. Preview the result and download the corrected image."
          }
        },
        {
          "@type": "Question",
          "name": "What is the shortcut for rotate image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "In our online tool, click 'Rotate Right' for 90° clockwise, 'Rotate Left' for 90° counter-clockwise, or '180°' for half rotation. You can also enter custom angles for precise rotation control."
          }
        },
        {
          "@type": "Question",
          "name": "Which tool or command is used to rotate an image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use our online image rotation tool with buttons for 'Rotate Right' (90°), 'Rotate Left' (-90°), '180°', or enter a custom angle. No software installation required - works directly in your web browser on any device."
          }
        }
      ]
    }
    </script>

    <style>
        /* Rotate Image Widget - Matching design with other tools */
        .rotate-image-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .rotate-image-widget-container * { box-sizing: border-box; }

        .rotate-image-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .rotate-image-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .rotate-image-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            background-color: var(--background-color-alt);
            text-align: center;
            cursor: pointer;
            transition: var(--transition-base);
            position: relative;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .rotate-image-upload-area:hover,
        .rotate-image-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .rotate-image-file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .rotate-image-upload-icon {
            font-size: 3rem;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-md);
        }

        .rotate-image-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .rotate-image-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.9rem;
        }

        .rotate-image-preview {
            display: none;
            text-align: center;
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .rotate-image-preview.show { display: block; }

        .rotate-image-preview-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.125rem;
            font-weight: 600;
        }

        .rotate-image-preview-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }

        .rotate-image-preview-section {
            text-align: center;
        }

        .rotate-image-preview-label {
            color: var(--text-color-light);
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .rotate-image-preview-image {
            max-width: 100%;
            max-height: 250px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            padding: var(--spacing-sm);
            display: block;
            margin: 0 auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: var(--transition-base);
        }

        .rotate-image-angle-info {
            margin-top: var(--spacing-sm);
            padding: var(--spacing-sm);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            font-weight: 600;
        }

        .rotate-image-preview-info {
            color: var(--text-color-light);
            font-size: 0.9rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .rotate-image-info-item {
            background: var(--card-bg);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .rotate-image-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .rotate-image-custom-section {
            grid-column: 1 / -1;
            display: flex;
            gap: var(--spacing-md);
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: var(--spacing-md);
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-color);
        }

        .rotate-image-angle-input {
            width: 100px;
            padding: var(--spacing-sm);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            background-color: var(--card-bg);
            color: var(--text-color);
            text-align: center;
            font-weight: 600;
        }

        .rotate-image-angle-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .rotate-image-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            min-width: 140px;
        }

        .rotate-image-btn:hover { transform: translateY(-2px); }

        .rotate-image-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .rotate-image-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .rotate-image-btn-primary:hover:not(:disabled) {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .rotate-image-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .rotate-image-btn-secondary:hover:not(:disabled) {
            background-color: var(--border-color);
        }

        .rotate-image-btn-success {
            background-color: #10b981;
            color: white;
        }

        .rotate-image-btn-success:hover:not(:disabled) {
            background-color: #059669;
        }

        .rotate-image-btn-warning {
            background-color: #f59e0b;
            color: white;
        }

        .rotate-image-btn-warning:hover:not(:disabled) {
            background-color: #d97706;
        }

        .rotate-image-btn-danger {
            background-color: #ef4444;
            color: white;
        }

        .rotate-image-btn-danger:hover:not(:disabled) {
            background-color: #dc2626;
        }

        .rotate-image-additional-controls {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
            justify-content: center;
        }

        .rotate-image-download-options {
            display: none;
            margin-bottom: var(--spacing-xl);
        }

        .rotate-image-download-options.show { display: block; }

        .rotate-image-download-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.125rem;
            font-weight: 600;
            text-align: center;
        }

        .rotate-image-download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--spacing-md);
        }

        .rotate-image-download-btn {
            padding: var(--spacing-md);
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            color: var(--text-color);
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            transition: var(--transition-base);
            cursor: pointer;
            display: block;
        }

        .rotate-image-download-btn:hover {
            border-color: var(--primary-color);
            background-color: var(--background-color-alt);
            transform: translateY(-2px);
            text-decoration: none;
            color: var(--primary-color);
        }

        .rotate-image-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .rotate-image-notification.show { transform: translateX(0); }
        
        .rotate-image-notification.error {
            background-color: #ef4444;
        }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .rotate-image-related-tool-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            margin: 0 auto var(--spacing-sm);
            transition: var(--transition-base);
            background: linear-gradient(145deg, #667eea, #764ba2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1), inset 0 1px 1px rgba(255, 255, 255, 0.1);
        }
        
        a[href*="flip-image"] .rotate-image-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }
        a[href*="image-to-base64"] .rotate-image-related-tool-icon { background: linear-gradient(145deg, #ff6b6b, #ee5a52); }
        a[href*="base64-to-image"] .rotate-image-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }

        a[href*="flip-image"]:hover .rotate-image-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        a[href*="image-to-base64"]:hover .rotate-image-related-tool-icon { background: linear-gradient(145deg, #ff7979, #fd6c6c); }
        a[href*="base64-to-image"]:hover .rotate-image-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        
        .rotate-image-related-tool-item:hover .rotate-image-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .rotate-image-related-tool-item { box-shadow: none; border: none; }
        .rotate-image-related-tool-item:hover { box-shadow: none; border: none; }
        .rotate-image-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .rotate-image-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .rotate-image-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .rotate-image-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .rotate-image-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .rotate-image-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .rotate-image-related-tool-item:hover .rotate-image-related-tool-name { color: var(--primary-color); }

        .rotate-image-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .rotate-image-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .rotate-image-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .rotate-image-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .rotate-image-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .rotate-image-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .rotate-image-widget-title { font-size: 1.875rem; }
            .rotate-image-controls { grid-template-columns: 1fr; }
            .rotate-image-custom-section { flex-direction: column; text-align: center; }
            .rotate-image-additional-controls { flex-direction: column; }
            .rotate-image-btn { flex: none; }
            .rotate-image-preview-container { grid-template-columns: 1fr; }
            .rotate-image-upload-area { min-height: 140px; padding: var(--spacing-lg); }
            .rotate-image-upload-icon { font-size: 2.5rem; }
            .rotate-image-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .rotate-image-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .rotate-image-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .rotate-image-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 600px) { 
            .rotate-image-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } 
            .rotate-image-download-grid { grid-template-columns: repeat(2, 1fr); }
        }

        @media (max-width: 480px) {
            .rotate-image-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .rotate-image-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .rotate-image-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .rotate-image-related-tool-name { font-size: 0.75rem; }
            .rotate-image-download-grid { grid-template-columns: 1fr; }
        }

        [data-theme="dark"] .rotate-image-upload-area:hover,
        [data-theme="dark"] .rotate-image-upload-area.dragover { background-color: rgba(96, 165, 250, 0.1); }
        .rotate-image-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
    </style>
</head>
<body>
    <div class="rotate-image-widget-container">
        <h1 class="rotate-image-widget-title">Rotate Image</h1>
        <p class="rotate-image-widget-description">
            Rotate images by 90°, 180°, 270° or any custom angle. Fix photo orientation on phone or computer instantly.
        </p>
        
        <div class="rotate-image-upload-area" id="uploadArea">
            <input type="file" class="rotate-image-file-input" id="fileInput" accept="image/*">
            <div class="rotate-image-upload-icon">🔄</div>
            <div class="rotate-image-upload-text">Drop an image here or click to browse</div>
            <div class="rotate-image-upload-subtext">Supports PNG, JPG, GIF, WebP, SVG, BMP</div>
        </div>

        <div class="rotate-image-preview" id="preview">
            <div class="rotate-image-preview-title">Image Preview:</div>
            <div class="rotate-image-preview-container">
                <div class="rotate-image-preview-section">
                    <div class="rotate-image-preview-label">Original</div>
                    <img class="rotate-image-preview-image" id="originalImage" alt="Original image">
                </div>
                <div class="rotate-image-preview-section">
                    <div class="rotate-image-preview-label">Rotated</div>
                    <img class="rotate-image-preview-image" id="previewImage" alt="Rotated image preview">
                    <div class="rotate-image-angle-info" id="angleInfo">Angle: 0°</div>
                </div>
            </div>
            <div class="rotate-image-preview-info" id="previewInfo"></div>
        </div>

        <div class="rotate-image-controls">
            <button class="rotate-image-btn rotate-image-btn-primary" id="rotateRightBtn" onclick="RotateImageTool.rotateRight()" disabled>
                Rotate Right (90°)
            </button>
            <button class="rotate-image-btn rotate-image-btn-warning" id="rotateLeftBtn" onclick="RotateImageTool.rotateLeft()" disabled>
                Rotate Left (-90°)
            </button>
            <button class="rotate-image-btn rotate-image-btn-danger" id="rotate180Btn" onclick="RotateImageTool.rotate180()" disabled>
                Rotate 180°
            </button>
            
            <div class="rotate-image-custom-section">
                <label for="customAngle">Custom Angle:</label>
                <input type="number" class="rotate-image-angle-input" id="customAngle" placeholder="0" min="-360" max="360" step="1" disabled>
                <button class="rotate-image-btn rotate-image-btn-success" id="applyCustomBtn" onclick="RotateImageTool.applyCustomAngle()" disabled>
                    Apply
                </button>
            </div>
        </div>

        <div class="rotate-image-additional-controls">
            <button class="rotate-image-btn rotate-image-btn-secondary" onclick="RotateImageTool.reset()" disabled id="resetBtn">
                Reset
            </button>
            <button class="rotate-image-btn rotate-image-btn-secondary" onclick="RotateImageTool.clear()">
                Clear All
            </button>
        </div>

        <div class="rotate-image-download-options" id="downloadOptions">
            <h3 class="rotate-image-download-title">Download Rotated Image:</h3>
            <div class="rotate-image-download-grid">
                <button class="rotate-image-download-btn" onclick="RotateImageTool.download('png')">
                    Download PNG
                </button>
                <button class="rotate-image-download-btn" onclick="RotateImageTool.download('jpg')">
                    Download JPG
                </button>
                <button class="rotate-image-download-btn" onclick="RotateImageTool.download('original')">
                    Download Original Format
                </button>
            </div>
        </div>

        <div class="rotate-image-related-tools">
            <h3 class="rotate-image-related-tools-title">Related Tools</h3>
            <div class="rotate-image-related-tools-grid">
                <a href="/p/flip-image.html" class="rotate-image-related-tool-item" rel="noopener">
                    <div class="rotate-image-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="rotate-image-related-tool-name">Flip Image</div>
                </a>

                <a href="/p/image-to-base64.html" class="rotate-image-related-tool-item" rel="noopener">
                    <div class="rotate-image-related-tool-icon">
                        <i class="fas fa-file-code"></i>
                    </div>
                    <div class="rotate-image-related-tool-name">Image to Base64</div>
                </a>

                <a href="/p/base64-to-image.html" class="rotate-image-related-tool-item" rel="noopener">
                    <div class="rotate-image-related-tool-icon">
                        <i class="fas fa-file-image"></i>
                    </div>
                    <div class="rotate-image-related-tool-name">Base64 to Image</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>How to Rotate Images Online</h2>
            <p>Rotating images is essential for correcting photo orientation, creating artistic effects, or preparing images for different layouts. Our online rotation tool works on any device and supports all common image formats.</p>
            
            <h3>Simple Rotation Process:</h3>
            <ol>
                <li><strong>Upload Image:</strong> Drag and drop your image file or click to browse. Works with PNG, JPG, GIF, WebP, SVG, and BMP files.</li>
                <li><strong>Choose Rotation:</strong> Use quick rotation buttons (90°, 180°, 270°) or enter a custom angle for precise control.</li>
                <li><strong>Preview & Download:</strong> Preview the rotated image side by side with the original, then download in your preferred format.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Image Rotation</h3>
            
            <h4>How do I rotate a photo on my phone or computer?</h4>
            <p>Upload your photo to our online rotation tool, then use the rotation buttons to rotate 90°, 180°, 270°, or enter a custom angle. Our tool works on any device - phone, tablet, or computer - directly in your browser. No app installation required.</p>
            
            <h4>Why won't my pictures rotate automatically?</h4>
            <p>Pictures may not auto-rotate due to missing or incorrect EXIF orientation data, browser limitations, or device settings. Our tool manually rotates images regardless of EXIF data, ensuring consistent results across all devices and platforms. This is especially common with images from older cameras or when EXIF data is stripped.</p>
            
            <h4>How do I fix the rotation of an image?</h4>
            <p>Upload your image to our rotation tool and use the rotation buttons to correct the orientation. Use 'Rotate Right' (90°) or 'Rotate Left' (-90°) to fix sideways images, or '180°' for upside-down images. You can preview the result in real-time and download the corrected image immediately.</p>
            
            <h4>What is the shortcut for rotate image?</h4>
            <p>In our online tool, click 'Rotate Right' for 90° clockwise rotation, 'Rotate Left' for 90° counter-clockwise, or '180°' for half rotation. For precise control, use the custom angle input to enter any degree value from -360° to +360°.</p>
            
            <h4>Which tool or command is used to rotate an image?</h4>
            <p>Use our online image rotation tool with buttons for 'Rotate Right' (90°), 'Rotate Left' (-90°), '180°', or enter a custom angle for precise rotation. No software installation required - works directly in your web browser on any device with full mobile support.</p>
            
            <h3>Common Rotation Scenarios</h3>
            <ul>
                <li><strong>Portrait to Landscape:</strong> Rotate 90° or -90° to change orientation</li>
                <li><strong>Upside Down Photos:</strong> Use 180° rotation to flip completely</li>
                <li><strong>Sideways Images:</strong> Rotate 90° clockwise or counter-clockwise</li>
                <li><strong>Artistic Effects:</strong> Use custom angles for creative tilted effects</li>
                <li><strong>Social Media:</strong> Rotate images to fit platform requirements</li>
                <li><strong>Document Scanning:</strong> Correct orientation of scanned documents</li>
                <li><strong>Photo Editing:</strong> Prepare images for specific layouts or designs</li>
            </ul>

            <h3>Rotation Angles Explained</h3>
            <ul>
                <li><strong>90° (Rotate Right):</strong> Clockwise quarter turn, portrait becomes landscape</li>
                <li><strong>-90° (Rotate Left):</strong> Counter-clockwise quarter turn</li>
                <li><strong>180°:</strong> Half turn, flips image upside down</li>
                <li><strong>270°:</strong> Three-quarter turn, same as -90°</li>
                <li><strong>Custom Angles:</strong> Any degree value for precise artistic control</li>
            </ul>

            <h3>Supported Image Formats</h3>
            <p>Our rotation tool supports all major image formats:</p>
            <ul>
                <li><strong>JPEG/JPG:</strong> Perfect for photographs and complex images</li>
                <li><strong>PNG:</strong> Ideal for images with transparency and graphics</li>
                <li><strong>GIF:</strong> Great for simple graphics (static frames only)</li>
                <li><strong>WebP:</strong> Modern format with excellent compression</li>
                <li><strong>SVG:</strong> Vector graphics maintain quality at any rotation</li>
                <li><strong>BMP:</strong> Uncompressed bitmap format</li>
            </ul>
            
            <h3>Tips for Best Results</h3>
            <ul>
                <li>Use PNG format to preserve transparency when rotating logos or icons</li>
                <li>For photos, JPEG format maintains good quality with smaller file sizes</li>
                <li>Custom angles may create transparent areas - PNG handles this better than JPEG</li>
                <li>Preview your rotated image before downloading to ensure correct orientation</li>
                <li>Use the reset function to quickly return to the original orientation</li>
                <li>Multiple 90° rotations are lossless, custom angles may introduce slight quality changes</li>
            </ul>
        </div>

        <div class="rotate-image-features">
            <h3 class="rotate-image-features-title">Key Features:</h3>
            <ul class="rotate-image-features-list">
                <li class="rotate-image-features-item" style="margin-bottom: 0.3em;">Quick rotation buttons (90°, 180°, 270°)</li>
                <li class="rotate-image-features-item" style="margin-bottom: 0.3em;">Custom angle input for precise control</li>
                <li class="rotate-image-features-item" style="margin-bottom: 0.3em;">Side-by-side preview comparison</li>
                <li class="rotate-image-features-item" style="margin-bottom: 0.3em;">Multiple output formats</li>
                <li class="rotate-image-features-item" style="margin-bottom: 0.3em;">Real-time angle display</li>
                <li class="rotate-image-features-item" style="margin-bottom: 0.3em;">Mobile-friendly interface</li>
                <li class="rotate-image-features-item" style="margin-bottom: 0.3em;">Drag & drop support</li>
                <li class="rotate-image-features-item">Works offline after loading</li>
            </ul>
        </div>
    </div>

    <!-- Notification -->
    <div class="rotate-image-notification" id="notification">
        Success!
    </div>

    <script>
        // Rotate Image Tool - Self-contained IIFE
        (function() {
            'use strict';

            let originalImageData = null;
            let currentImageData = null;
            let originalFileName = '';
            let originalFileType = '';
            let currentAngle = 0;

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                preview: () => document.getElementById('preview'),
                originalImage: () => document.getElementById('originalImage'),
                previewImage: () => document.getElementById('previewImage'),
                previewInfo: () => document.getElementById('previewInfo'),
                angleInfo: () => document.getElementById('angleInfo'),
                rotateRightBtn: () => document.getElementById('rotateRightBtn'),
                rotateLeftBtn: () => document.getElementById('rotateLeftBtn'),
                rotate180Btn: () => document.getElementById('rotate180Btn'),
                customAngle: () => document.getElementById('customAngle'),
                applyCustomBtn: () => document.getElementById('applyCustomBtn'),
                resetBtn: () => document.getElementById('resetBtn'),
                downloadOptions: () => document.getElementById('downloadOptions'),
                notification: () => document.getElementById('notification')
            };

            window.RotateImageTool = {
                loadImage(file) {
                    originalFileName = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;
                    originalFileType = file.type;
                    
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        originalImageData = e.target.result;
                        currentImageData = originalImageData;
                        currentAngle = 0;
                        
                        elements.originalImage().src = originalImageData;
                        elements.previewImage().src = originalImageData;
                        elements.angleInfo().textContent = 'Angle: 0°';
                        
                        // Get image dimensions
                        const img = new Image();
                        img.onload = () => {
                            elements.previewInfo().innerHTML = `
                                <div class="rotate-image-info-item"><strong>Name:</strong> ${file.name}</div>
                                <div class="rotate-image-info-item"><strong>Size:</strong> ${(file.size / 1024).toFixed(1)} KB</div>
                                <div class="rotate-image-info-item"><strong>Dimensions:</strong> ${img.width} × ${img.height}px</div>
                                <div class="rotate-image-info-item"><strong>Type:</strong> ${file.type}</div>
                            `;
                        };
                        img.src = originalImageData;
                        
                        elements.preview().classList.add('show');
                        this.enableControls();
                        elements.downloadOptions().classList.add('show');
                    };
                    reader.readAsDataURL(file);
                },

                enableControls() {
                    elements.rotateRightBtn().disabled = false;
                    elements.rotateLeftBtn().disabled = false;
                    elements.rotate180Btn().disabled = false;
                    elements.customAngle().disabled = false;
                    elements.applyCustomBtn().disabled = false;
                    elements.resetBtn().disabled = false;
                },

                rotateRight() {
                    this.applyRotation(currentAngle + 90);
                    this.showNotification('Rotated 90° clockwise');
                },

                rotateLeft() {
                    this.applyRotation(currentAngle - 90);
                    this.showNotification('Rotated 90° counter-clockwise');
                },

                rotate180() {
                    this.applyRotation(currentAngle + 180);
                    this.showNotification('Rotated 180°');
                },

                applyCustomAngle() {
                    const customAngleInput = elements.customAngle();
                    const angle = parseFloat(customAngleInput.value);
                    
                    if (isNaN(angle)) {
                        this.showNotification('Please enter a valid angle', 'error');
                        return;
                    }
                    
                    if (angle < -360 || angle > 360) {
                        this.showNotification('Angle must be between -360° and 360°', 'error');
                        return;
                    }
                    
                    this.applyRotation(angle);
                    this.showNotification(`Applied custom rotation: ${angle}°`);
                },

                applyRotation(angle) {
                    // Normalize angle to -360 to 360 range
                    currentAngle = angle % 360;
                    if (currentAngle > 180) currentAngle -= 360;
                    if (currentAngle < -180) currentAngle += 360;
                    
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const img = new Image();
                    
                    img.onload = () => {
                        const angleRad = (currentAngle * Math.PI) / 180;
                        
                        // Calculate new canvas dimensions for rotated image
                        const cos = Math.abs(Math.cos(angleRad));
                        const sin = Math.abs(Math.sin(angleRad));
                        const newWidth = img.width * cos + img.height * sin;
                        const newHeight = img.width * sin + img.height * cos;
                        
                        canvas.width = newWidth;
                        canvas.height = newHeight;
                        
                        // Move to center and rotate
                        ctx.translate(newWidth / 2, newHeight / 2);
                        ctx.rotate(angleRad);
                        ctx.drawImage(img, -img.width / 2, -img.height / 2);
                        
                        currentImageData = canvas.toDataURL(originalFileType);
                        elements.previewImage().src = currentImageData;
                        elements.angleInfo().textContent = `Angle: ${currentAngle}°`;
                        elements.customAngle().value = currentAngle;
                    };
                    
                    img.src = originalImageData;
                },

                reset() {
                    currentImageData = originalImageData;
                    currentAngle = 0;
                    elements.previewImage().src = originalImageData;
                    elements.angleInfo().textContent = 'Angle: 0°';
                    elements.customAngle().value = '0';
                    this.showNotification('Image reset to original');
                },

                download(format) {
                    if (!currentImageData) {
                        this.showNotification('Please select an image first', 'error');
                        return;
                    }
                    
                    let fileName = originalFileName;
                    let mimeType = originalFileType;
                    
                    if (format === 'png') {
                        fileName += '_rotated.png';
                        mimeType = 'image/png';
                    } else if (format === 'jpg') {
                        fileName += '_rotated.jpg';
                        mimeType = 'image/jpeg';
                    } else {
                        const ext = originalFileType.split('/')[1] || 'png';
                        fileName += `_rotated.${ext}`;
                    }
                    
                    if (format === 'original' || mimeType === originalFileType) {
                        this.downloadDirect(fileName);
                    } else {
                        this.downloadAsFormat(fileName, mimeType, format);
                    }
                },

                downloadDirect(fileName) {
                    const link = document.createElement('a');
                    link.href = currentImageData;
                    link.download = fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    this.showNotification('Image downloaded successfully');
                },

                downloadAsFormat(fileName, mimeType, format) {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const img = new Image();
                    
                    img.onload = () => {
                        canvas.width = img.width;
                        canvas.height = img.height;
                        
                        // Add white background for JPG
                        if (format === 'jpg') {
                            ctx.fillStyle = 'white';
                            ctx.fillRect(0, 0, canvas.width, canvas.height);
                        }
                        
                        ctx.drawImage(img, 0, 0);
                        
                        canvas.toBlob((blob) => {
                            const url = URL.createObjectURL(blob);
                            const link = document.createElement('a');
                            link.href = url;
                            link.download = fileName;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(url);
                            
                            this.showNotification(`Downloaded as ${format.toUpperCase()}`);
                        }, mimeType, format === 'jpg' ? 0.9 : undefined);
                    };
                    
                    img.src = currentImageData;
                },

                clear() {
                    elements.fileInput().value = '';
                    elements.preview().classList.remove('show');
                    elements.downloadOptions().classList.remove('show');
                    
                    // Disable controls
                    elements.rotateRightBtn().disabled = true;
                    elements.rotateLeftBtn().disabled = true;
                    elements.rotate180Btn().disabled = true;
                    elements.customAngle().disabled = true;
                    elements.applyCustomBtn().disabled = true;
                    elements.resetBtn().disabled = true;
                    
                    originalImageData = null;
                    currentImageData = null;
                    originalFileName = '';
                    originalFileType = '';
                    currentAngle = 0;
                    elements.customAngle().value = '';
                },

                showNotification(message, type = 'success') {
                    const notification = elements.notification();
                    notification.textContent = message;
                    notification.classList.remove('show', 'error');
                    if (type === 'error') notification.classList.add('error');
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 3000);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();
                const customAngle = elements.customAngle();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // File input change
                fileInput.addEventListener('change', function() {
                    if (this.files.length > 0 && this.files[0].type.startsWith('image/')) {
                        RotateImageTool.loadImage(this.files[0]);
                    }
                });

                // Custom angle input - Enter key to apply
                customAngle.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        RotateImageTool.applyCustomAngle();
                    }
                });

                // Drag and drop
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    
                    const files = e.dataTransfer.files;
                    if (files.length > 0 && files[0].type.startsWith('image/')) {
                        fileInput.files = files;
                        RotateImageTool.loadImage(files[0]);
                    }
                });

                // Click to upload
                uploadArea.addEventListener('click', function() {
                    fileInput.click();
                });
            });
        })();
    </script>
</body>
</html>