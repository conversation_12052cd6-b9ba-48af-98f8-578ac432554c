<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JPG Converter - Free Online Image to JPG Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free JPG Converter - Convert Any Image to JPG Online",
        "description": "Convert any image format to JPG instantly. Support for PNG, BMP, GIF, WEBP, and more. Free online tool with quality control and batch conversion.",
        "url": "https://www.webtoolskit.org/p/jpg-converter_23.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "JPG Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Image to JPG" },
            { "@type": "DownloadAction", "name": "Download Converted JPG" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I convert a photo to JPG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Upload your photo in any format (PNG, BMP, GIF, WEBP, etc.) using our converter. Adjust the quality settings if needed, click convert, and download your JPG file. The process is instant and works with all common image formats."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert image to JPG for free?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use our free online JPG converter. Simply upload your image file, choose quality settings, and convert instantly. No registration, watermarks, or hidden fees. All processing happens locally in your browser for complete privacy."
          }
        },
        {
          "@type": "Question",
          "name": "Are online JPG converters safe?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our JPG converter is completely safe as it processes images locally in your browser without uploading files to servers. Your images never leave your device, ensuring complete privacy and security. No data is stored or transmitted."
          }
        },
        {
          "@type": "Question",
          "name": "What is the best JPEG converter?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The best JPEG converter offers multiple format support, quality control, fast processing, and privacy protection. Our tool supports all major formats, provides adjustable quality settings, and processes everything locally for maximum security."
          }
        },
        {
          "@type": "Question",
          "name": "How do I convert a PNG file to a JPG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Upload your PNG file to our converter, adjust the quality slider to your preference (85% recommended), click convert, and download the JPG result. Transparent areas in PNG will be filled with white background in the JPG output."
          }
        }
      ]
    }
    </script>

    <style>
        /* JPG Converter Widget - Simplified & Template Compatible */
        .jpg-converter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .jpg-converter-widget-container * { box-sizing: border-box; }

        .jpg-converter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .jpg-converter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .jpg-converter-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            margin-bottom: var(--spacing-xl);
            cursor: pointer;
        }

        .jpg-converter-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .jpg-converter-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .jpg-converter-upload-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .jpg-converter-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .jpg-converter-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .jpg-converter-file-input {
            display: none;
        }

        .jpg-converter-format-info {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .jpg-converter-format-text {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .jpg-converter-format-badge {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            margin: 0 0.25rem;
        }

        .jpg-converter-quality-control {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            border: 1px solid var(--border-color);
        }

        .jpg-converter-quality-title {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }

        .jpg-converter-quality-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: var(--border-color);
            outline: none;
            margin-bottom: var(--spacing-sm);
        }

        .jpg-converter-quality-value {
            color: var(--text-color-light);
            font-size: 0.875rem;
            text-align: center;
        }

        .jpg-converter-preview {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            border: 1px solid var(--border-color);
        }

        .jpg-converter-preview-title {
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            text-align: center;
        }

        .jpg-converter-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .jpg-converter-preview-item {
            text-align: center;
            flex: 1;
            min-width: 200px;
        }

        .jpg-converter-preview-label {
            color: var(--text-color-light);
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .jpg-converter-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .jpg-converter-file-info {
            margin-top: var(--spacing-sm);
            font-size: 0.75rem;
            color: var(--text-color-light);
        }

        .jpg-converter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .jpg-converter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .jpg-converter-btn:hover { transform: translateY(-2px); }

        .jpg-converter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .jpg-converter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .jpg-converter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .jpg-converter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .jpg-converter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .jpg-converter-btn-success:hover {
            background-color: #059669;
        }

        .jpg-converter-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .jpg-converter-btn:disabled:hover {
            transform: none;
        }

        .jpg-converter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .jpg-converter-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        .jpg-converter-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .jpg-converter-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .jpg-converter-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .jpg-converter-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .jpg-converter-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .jpg-converter-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="png-converter"] .jpg-converter-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-resizer"] .jpg-converter-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="image-compressor"] .jpg-converter-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .jpg-converter-related-tool-item:hover .jpg-converter-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="png-converter"]:hover .jpg-converter-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-resizer"]:hover .jpg-converter-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="image-compressor"]:hover .jpg-converter-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .jpg-converter-related-tool-item { box-shadow: none; border: none; }
        .jpg-converter-related-tool-item:hover { box-shadow: none; border: none; }
        .jpg-converter-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .jpg-converter-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .jpg-converter-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .jpg-converter-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .jpg-converter-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .jpg-converter-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .jpg-converter-related-tool-item:hover .jpg-converter-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .jpg-converter-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .jpg-converter-widget-title { font-size: 1.875rem; }
            .jpg-converter-buttons { flex-direction: column; }
            .jpg-converter-btn { flex: none; }
            .jpg-converter-preview-content { flex-direction: column; }
            .jpg-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .jpg-converter-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .jpg-converter-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .jpg-converter-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .jpg-converter-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .jpg-converter-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .jpg-converter-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .jpg-converter-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .jpg-converter-upload-area:hover { background-color: var(--card-bg); }
        .jpg-converter-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .jpg-converter-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .jpg-converter-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="jpg-converter-widget-container">
        <h1 class="jpg-converter-widget-title">JPG Converter</h1>
        <p class="jpg-converter-widget-description">
            Convert any image format to JPG instantly. Support for PNG, BMP, GIF, WEBP, and more with quality control and file size optimization.
        </p>
        
        <div class="jpg-converter-upload-area" id="uploadArea">
            <div class="jpg-converter-upload-icon">📁</div>
            <div class="jpg-converter-upload-text">Click to upload or drag & drop your image file</div>
            <div class="jpg-converter-upload-subtext">Supports PNG, BMP, GIF, WEBP, TIFF and more up to 10MB</div>
            <input type="file" id="fileInput" class="jpg-converter-file-input" accept="image/*" />
        </div>

        <div class="jpg-converter-format-info" id="formatInfo">
            <div class="jpg-converter-format-text">
                Detected format: <span class="jpg-converter-format-badge" id="formatBadge">Unknown</span>
                Converting to: <span class="jpg-converter-format-badge">JPG</span>
            </div>
        </div>

        <div class="jpg-converter-quality-control" id="qualityControl">
            <div class="jpg-converter-quality-title">Quality Settings</div>
            <input type="range" id="qualitySlider" class="jpg-converter-quality-slider" min="10" max="100" value="85" />
            <div class="jpg-converter-quality-value">Quality: <span id="qualityValue">85</span>%</div>
        </div>

        <div class="jpg-converter-preview" id="previewSection">
            <h3 class="jpg-converter-preview-title">Image Preview</h3>
            <div class="jpg-converter-preview-content">
                <div class="jpg-converter-preview-item">
                    <div class="jpg-converter-preview-label">Original</div>
                    <img id="originalImage" class="jpg-converter-preview-image" alt="Original Image" />
                    <div class="jpg-converter-file-info" id="originalInfo"></div>
                </div>
                <div class="jpg-converter-preview-item">
                    <div class="jpg-converter-preview-label">Converted (JPG)</div>
                    <img id="convertedImage" class="jpg-converter-preview-image" alt="Converted JPG" />
                    <div class="jpg-converter-file-info" id="convertedInfo"></div>
                </div>
            </div>
        </div>

        <div class="jpg-converter-buttons">
            <button id="convertBtn" class="jpg-converter-btn jpg-converter-btn-primary" disabled>
                Convert to JPG
            </button>
            <button id="downloadBtn" class="jpg-converter-btn jpg-converter-btn-success" disabled>
                Download JPG
            </button>
            <button id="resetBtn" class="jpg-converter-btn jpg-converter-btn-secondary">
                Reset
            </button>
        </div>

        <div class="jpg-converter-related-tools">
            <h3 class="jpg-converter-related-tools-title">Related Tools</h3>
            <div class="jpg-converter-related-tools-grid">
                <a href="/p/png-converter.html" class="jpg-converter-related-tool-item" rel="noopener">
                    <div class="jpg-converter-related-tool-icon">
                        <i class="fas fa-file-image"></i>
                    </div>
                    <div class="jpg-converter-related-tool-name">PNG Converter</div>
                </a>

                <a href="/p/image-resizer.html" class="jpg-converter-related-tool-item" rel="noopener">
                    <div class="jpg-converter-related-tool-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div class="jpg-converter-related-tool-name">Image Resizer</div>
                </a>

                <a href="/p/image-compressor.html" class="jpg-converter-related-tool-item" rel="noopener">
                    <div class="jpg-converter-related-tool-icon">
                        <i class="fas fa-compress-alt"></i>
                    </div>
                    <div class="jpg-converter-related-tool-name">Image Compressor</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Free JPG Converter Online</h2>
            <p>Transform any image format to JPG effortlessly with our comprehensive online converter. Supporting PNG, BMP, GIF, WEBP, TIFF, and many other formats, our tool provides professional-quality conversion with adjustable compression settings for optimal results.</p>
            
            <p>Converting images to JPG format offers excellent compression for photographs and complex images, significantly reducing file sizes while maintaining visual quality. Our converter processes everything locally in your browser, ensuring complete privacy and instant results without uploading files to servers.</p>

            <h3>Why Use Our JPG Converter?</h3>
            <p>JPG format is universally supported and ideal for web use, email sharing, and storage optimization. Our converter handles multiple input formats seamlessly, provides quality control options, and maintains image integrity throughout the conversion process.</p>

            <p>Whether you're converting PNG files for web optimization, BMP images for compatibility, or any other format for professional use, our tool delivers consistent, high-quality results with complete format flexibility and user control.</p>

            <h3>Frequently Asked Questions About JPG Conversion</h3>
            
            <h4>How do I convert a photo to JPG?</h4>
            <p>Upload your photo in any format (PNG, BMP, GIF, WEBP, etc.) using our converter. Adjust the quality settings if needed, click convert, and download your JPG file. The process is instant and works with all common image formats including RAW files from cameras.</p>
            
            <h4>How to convert image to JPG for free?</h4>
            <p>Use our free online JPG converter. Simply upload your image file, choose quality settings, and convert instantly. No registration, watermarks, or hidden fees. All processing happens locally in your browser for complete privacy and unlimited conversions.</p>
            
            <h4>Are online JPG converters safe?</h4>
            <p>Our JPG converter is completely safe as it processes images locally in your browser without uploading files to servers. Your images never leave your device, ensuring complete privacy and security. No data is stored, transmitted, or accessible to third parties.</p>
            
            <h4>What is the best JPEG converter?</h4>
            <p>The best JPEG converter offers multiple format support, quality control, fast processing, and privacy protection. Our tool supports all major formats, provides adjustable quality settings, processes everything locally for maximum security, and delivers professional results instantly.</p>
            
            <h4>How do I convert a PNG file to a JPG?</h4>
            <p>Upload your PNG file to our converter, adjust the quality slider to your preference (85% recommended), click convert, and download the JPG result. Transparent areas in PNG will be filled with white background in the JPG output, maintaining image quality and compatibility.</p>
        </div>

        <div class="jpg-converter-features">
            <h3 class="jpg-converter-features-title">Key Features</h3>
            <ul class="jpg-converter-features-list">
                <li class="jpg-converter-features-item">Multiple format support (PNG, BMP, GIF, WEBP, TIFF)</li>
                <li class="jpg-converter-features-item">Adjustable quality control</li>
                <li class="jpg-converter-features-item">Local browser processing</li>
                <li class="jpg-converter-features-item">No file upload required</li>
                <li class="jpg-converter-features-item">Instant conversion results</li>
                <li class="jpg-converter-features-item">Drag and drop interface</li>
                <li class="jpg-converter-features-item">File size optimization</li>
                <li class="jpg-converter-features-item">Mobile-friendly design</li>
            </ul>
        </div>
    </div>

    <div id="notification" class="jpg-converter-notification"></div>

    <script>
        (function() {
            'use strict';
            
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const formatInfo = document.getElementById('formatInfo');
            const formatBadge = document.getElementById('formatBadge');
            const qualityControl = document.getElementById('qualityControl');
            const qualitySlider = document.getElementById('qualitySlider');
            const qualityValue = document.getElementById('qualityValue');
            const previewSection = document.getElementById('previewSection');
            const originalImage = document.getElementById('originalImage');
            const convertedImage = document.getElementById('convertedImage');
            const originalInfo = document.getElementById('originalInfo');
            const convertedInfo = document.getElementById('convertedInfo');
            const convertBtn = document.getElementById('convertBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            const resetBtn = document.getElementById('resetBtn');
            const notification = document.getElementById('notification');
            
            let currentFile = null;
            let convertedBlob = null;
            
            // Upload area events
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            
            // File input change
            fileInput.addEventListener('change', handleFileSelect);
            
            // Quality slider
            qualitySlider.addEventListener('input', updateQualityValue);
            qualitySlider.addEventListener('change', reconvertIfNeeded);
            
            // Button events
            convertBtn.addEventListener('click', convertImage);
            downloadBtn.addEventListener('click', downloadImage);
            resetBtn.addEventListener('click', resetTool);
            
            function handleDragOver(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            }
            
            function handleDragLeave(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            }
            
            function handleDrop(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    processFile(files[0]);
                }
            }
            
            function handleFileSelect(e) {
                const file = e.target.files[0];
                if (file) {
                    processFile(file);
                }
            }
            
            function updateQualityValue() {
                qualityValue.textContent = qualitySlider.value;
            }
            
            function reconvertIfNeeded() {
                if (currentFile && convertedBlob) {
                    convertImage();
                }
            }
            
            function processFile(file) {
                if (!file.type.match(/^image\//)) {
                    showNotification('Please select a valid image file.', 'error');
                    return;
                }
                
                if (file.size > 10 * 1024 * 1024) {
                    showNotification('File size must be less than 10MB.', 'error');
                    return;
                }
                
                currentFile = file;
                displayFormatInfo(file);
                displayPreview(file);
                qualityControl.style.display = 'block';
                convertBtn.disabled = false;
            }
            
            function displayFormatInfo(file) {
                const format = getFileFormat(file.type);
                formatBadge.textContent = format;
                formatInfo.style.display = 'block';
            }
            
            function getFileFormat(mimeType) {
                const formats = {
                    'image/png': 'PNG',
                    'image/jpeg': 'JPG',
                    'image/gif': 'GIF',
                    'image/bmp': 'BMP',
                    'image/webp': 'WEBP',
                    'image/tiff': 'TIFF',
                    'image/svg+xml': 'SVG'
                };
                return formats[mimeType] || 'Unknown';
            }
            
            function displayPreview(file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    originalImage.src = e.target.result;
                    originalInfo.textContent = `Format: ${getFileFormat(file.type)} | Size: ${formatFileSize(file.size)}`;
                    previewSection.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
            
            function convertImage() {
                if (!currentFile) return;
                
                convertBtn.textContent = 'Converting...';
                convertBtn.disabled = true;
                
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    // Fill with white background (JPG doesn't support transparency)
                    ctx.fillStyle = '#FFFFFF';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // Draw image on canvas
                    ctx.drawImage(img, 0, 0);
                    
                    // Convert to JPG blob with quality setting
                    const quality = qualitySlider.value / 100;
                    canvas.toBlob(function(blob) {
                        convertedBlob = blob;
                        
                        // Display converted image
                        const url = URL.createObjectURL(blob);
                        convertedImage.src = url;
                        const reduction = Math.round((1 - blob.size / currentFile.size) * 100);
                        convertedInfo.textContent = `Format: JPG | Size: ${formatFileSize(blob.size)} (${reduction > 0 ? reduction + '% smaller' : 'larger'})`;
                        
                        // Enable download button
                        downloadBtn.disabled = false;
                        convertBtn.textContent = 'Convert to JPG';
                        convertBtn.disabled = false;
                        
                        showNotification('Image converted to JPG successfully!', 'success');
                    }, 'image/jpeg', quality);
                };
                
                img.src = originalImage.src;
            }
            
            function downloadImage() {
                if (!convertedBlob) return;
                
                const url = URL.createObjectURL(convertedBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = getFileName(currentFile.name) + '.jpg';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                showNotification('JPG file downloaded!', 'success');
            }
            
            function resetTool() {
                currentFile = null;
                convertedBlob = null;
                fileInput.value = '';
                formatInfo.style.display = 'none';
                qualityControl.style.display = 'none';
                previewSection.style.display = 'none';
                convertBtn.disabled = true;
                downloadBtn.disabled = true;
                convertBtn.textContent = 'Convert to JPG';
                qualitySlider.value = 85;
                qualityValue.textContent = '85';
                uploadArea.classList.remove('dragover');
            }
            
            function getFileName(fullName) {
                return fullName.substring(0, fullName.lastIndexOf('.')) || fullName;
            }
            
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            function showNotification(message, type = 'success') {
                notification.textContent = message;
                notification.className = 'jpg-converter-notification show';
                
                if (type === 'error') {
                    notification.style.backgroundColor = '#dc3545';
                } else {
                    notification.style.backgroundColor = '#10b981';
                }
                
                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
        })();
    </script>
</body>
</html>