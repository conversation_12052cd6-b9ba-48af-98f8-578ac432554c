<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Repeater Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Text Repeater - Repeat Text Multiple Times",
        "description": "Repeat any text or phrase multiple times instantly. Free online tool with customizable separators, repeat counts, and formatting options.",
        "url": "https://www.webtoolskit.org/p/text-repeater.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Text Repeater",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Repeat Text" },
            { "@type": "CopyAction", "name": "Copy Repeated Text" }
        ]
    }
    </script>

    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a text repeater tool used for?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A text repeater tool is used to duplicate a piece of text (a word, phrase, or emoji) a specified number of times. It's useful for creating test data, generating patterns for design, making fun social media messages, or quickly creating long strings of text without manual copying and pasting."
          }
        },
        {
          "@type": "Question",
          "name": "How can I repeat a word multiple times online?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using an online text repeater is the easiest way. Simply enter the word or phrase into the input box, set the 'Repeat Count' to the desired number, choose a separator (like a space or new line), and the tool will instantly generate the repeated text for you to copy."
          }
        },
        {
          "@type": "Question",
          "name": "Is there a way to generate repeated text automatically?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, this Text Repeater tool generates the repeated text automatically. As you type or change the options like the repeat count or separator, the output field updates in real-time, giving you an immediate preview of the result."
          }
        },
        {
          "@type": "Question",
          "name": "How do I create spam-style text for testing?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To create long, repetitive 'spam-style' text for testing purposes (e.g., for data validation or load testing), enter a phrase into the tool, set a very high repeat count, and choose 'New Line' or no separator. This will quickly generate a large block of text that mimics spam messages."
          }
        },
        {
          "@type": "Question",
          "name": "Can I use a text repeater for social media or chat messages?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Absolutely. A text repeater is perfect for creating fun and emphatic messages for social media platforms like TikTok, Instagram, or WhatsApp. You can repeat an emoji, a word, or a short phrase to make your messages stand out and grab attention."
          }
        }
      ]
    }
    </script>


    <style>
        /* Text Repeater Widget - Simplified & Template Compatible */
        .text-repeater-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .text-repeater-widget-container * { box-sizing: border-box; }

        .text-repeater-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-repeater-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .text-repeater-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .text-repeater-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .text-repeater-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .text-repeater-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .text-repeater-control-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .text-repeater-input {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            background-color: var(--card-bg);
            color: var(--text-color);
            transition: var(--transition-base);
        }

        .text-repeater-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .text-repeater-select {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            background-color: var(--card-bg);
            color: var(--text-color);
            transition: var(--transition-base);
            cursor: pointer;
        }

        .text-repeater-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .text-repeater-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .text-repeater-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .text-repeater-btn:hover { transform: translateY(-2px); }

        .text-repeater-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .text-repeater-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .text-repeater-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .text-repeater-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .text-repeater-btn-success {
            background-color: #10b981;
            color: white;
        }

        .text-repeater-btn-success:hover {
            background-color: #059669;
        }

        .text-repeater-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .text-repeater-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-repeater-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 120px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .text-repeater-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .text-repeater-notification.show { transform: translateX(0); }
        
        /* SEO Content Section Styles */
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        
        /* === START: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        .related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="text-sorter"] .related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="comma-separator"] .related-tool-icon { background: linear-gradient(145deg, #F97316, #EA580C); }
        a[href*="case-converter"] .related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }

        .related-tool-item:hover .related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .related-tool-item {
            box-shadow: none; border: none; text-align: center; text-decoration: none; color: inherit;
            transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg);
            display: block; width: 100%; max-width: 160px;
        }
        
        .related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        
        .related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .related-tool-item:hover .related-tool-name { color: var(--primary-color); }
        
        /* === END: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        /* === START: STANDARDIZED FEATURES SECTION === */
        .features { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .features-title { color: var(--text-color); margin-bottom: var(--spacing-md); font-size: 1.25rem; font-weight: 700; }
        .features-list { list-style: none; padding: 0; margin: 0; columns: 2; -webkit-columns: 2; -moz-columns: 2; }
        .features-item { padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg); color: var(--text-color-light); position: relative; margin-bottom: 0.3em; }
        .features-item:before { content: ""; position: absolute; left: 0; top: calc(var(--spacing-sm) + 2px); width: 12px; height: 6px; border-left: 2px solid #10b981; border-bottom: 2px solid #10b981; transform: rotate(-45deg); }
        
        @media (max-width: 600px) { .features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
        /* === END: STANDARDIZED FEATURES SECTION === */

        /* Responsive Design */
        @media (max-width: 768px) {
            .text-repeater-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .text-repeater-widget-title { font-size: 1.875rem; }
            .text-repeater-buttons { flex-direction: column; }
            .text-repeater-btn { flex: none; }
            .text-repeater-controls { grid-template-columns: 1fr; }
            .related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .related-tool-item { padding: var(--spacing-md); max-width: none; }
            .related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .related-tool-name { font-size: 0.75rem; }
        }
        
        /* Dark Mode Support */
        [data-theme="dark"] .text-repeater-textarea:focus,
        [data-theme="dark"] .text-repeater-input:focus,
        [data-theme="dark"] .text-repeater-select:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        /* Focus & Accessibility */
        .text-repeater-btn:focus,
        .text-repeater-input:focus,
        .text-repeater-select:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .text-repeater-output::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="text-repeater-widget-container">
        <h1 class="text-repeater-widget-title">Text Repeater</h1>
        <p class="text-repeater-widget-description">
            Repeat any text or phrase multiple times with customizable separators and formatting options. Perfect for creating patterns, testing, and content generation.
        </p>
        
        <div class="text-repeater-input-group">
            <label for="textRepeaterInput" class="text-repeater-label">Enter text to repeat:</label>
            <textarea 
                id="textRepeaterInput" 
                class="text-repeater-textarea"
                placeholder="Type or paste the text you want to repeat..."
                rows="4"
            ></textarea>
        </div>

        <div class="text-repeater-controls">
            <div class="text-repeater-control-group">
                <label for="repeatCount" class="text-repeater-label">Repeat Count:</label>
                <input type="number" id="repeatCount" class="text-repeater-input" value="5" min="1" max="1000">
            </div>
            <div class="text-repeater-control-group">
                <label for="separatorType" class="text-repeater-label">Separator:</label>
                <select id="separatorType" class="text-repeater-select">
                    <option value="newline">New Line</option>
                    <option value="space">Space</option>
                    <option value="comma">Comma</option>
                    <option value="comma-space">Comma + Space</option>
                    <option value="semicolon">Semicolon</option>
                    <option value="pipe">Pipe (|)</option>
                    <option value="dash">Dash (-)</option>
                    <option value="custom">Custom</option>
                </select>
            </div>
            <div class="text-repeater-control-group" id="customSeparatorGroup" style="display: none;">
                <label for="customSeparator" class="text-repeater-label">Custom Separator:</label>
                <input type="text" id="customSeparator" class="text-repeater-input" placeholder="Enter custom separator">
            </div>
        </div>

        <div class="text-repeater-buttons">
            <button class="text-repeater-btn text-repeater-btn-primary" onclick="TextRepeater.repeat()">
                Repeat Text
            </button>
            <button class="text-repeater-btn text-repeater-btn-secondary" onclick="TextRepeater.clear()">
                Clear All
            </button>
            <button class="text-repeater-btn text-repeater-btn-success" onclick="TextRepeater.copy()">
                Copy Result
            </button>
        </div>

        <div class="text-repeater-result">
            <h3 class="text-repeater-result-title">Repeated Text:</h3>
            <div class="text-repeater-output" id="textRepeaterOutput">
                Your repeated text will appear here...
            </div>
        </div>
        
        <div class="related-tools">
            <h3 class="related-tools-title">Related Tools</h3>
            <div class="related-tools-grid">
                <a href="/p/text-sorter.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon"><i class="fas fa-sort-alpha-down"></i></div>
                    <div class="related-tool-name">Text Sorter</div>
                </a>
                <a href="/p/comma-separator.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon"><i class="fas fa-list"></i></div>
                    <div class="related-tool-name">Comma Separator</div>
                </a>
                <a href="/p/case-converter.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon"><i class="fas fa-text-height"></i></div>
                    <div class="related-tool-name">Case Converter</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Multiply Your Text Instantly with Our Text Repeater</h2>
            <p>A <strong>Text Repeater</strong> is a simple yet powerful online tool that allows you to duplicate any word, phrase, or string of characters multiple times in an instant. Instead of tedious manual copying and pasting, this tool automates the process, saving you time and effort. It's perfect for a wide range of tasks, from generating test data for software development to creating fun, eye-catching text for social media posts, or even producing patterns for creative projects.</p>
            
            <h3>How to Use the Text Repeater</h3>
            <p>Our tool is designed for maximum efficiency. Just follow these easy steps:</p>
            <ol>
                <li><strong>Enter Your Text:</strong> Type or paste the text you wish to repeat into the main input box.</li>
                <li><strong>Set the Repeat Count:</strong> Specify how many times you want the text to be duplicated.</li>
                <li><strong>Choose a Separator:</strong> Select how you want each repetition to be separated—by a new line, a space, a comma, or even a custom character of your choice.</li>
                <li><strong>Copy Your Result:</strong> The tool generates the repeated text in real-time. Simply click the "Copy Result" button to save it to your clipboard.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Text Repeater</h3>
            
            <h4>What is a text repeater tool used for?</h4>
            <p>A text repeater is used to quickly generate multiple copies of a piece of text. Common uses include creating large blocks of sample data for testing software, making patterns for text art, emphasizing a point in a chat message, and generating repetitive content for social media bios or posts.</p>
            
            <h4>How can I repeat a word multiple times online?</h4>
            <p>Using an online text repeater like this one is the most efficient method. You just need to enter the word, specify the number of repetitions, choose a separator (like a space, comma, or new line), and the tool will instantly create the full string of repeated text for you.</p>
            
            <h4>Is there a way to generate repeated text automatically?</h4>
            <p>Yes, this tool is fully automatic. As you type your text or adjust the repeat count and separator options in the controls, the output field updates instantly, providing a live preview of the final text. This allows you to experiment with different settings and see the results immediately.</p>
            
            <h4>How do I create spam-style text for testing?</h4>
            <p>To generate "spam-style" text, which is often long and highly repetitive, you can use our tool to simulate it. Enter a short phrase or a string of emojis, set a high repeat count (e.g., 200), and choose a simple separator like a newline. This is useful for testing how applications or filters handle such content.</p>
            
            <h4>Can I use a text repeater for social media or chat messages?</h4>
            <p>Absolutely! A text repeater is a popular tool for creating fun and emphatic messages on platforms like WhatsApp, TikTok, Instagram, and Discord. Repeating a word or emoji (e.g., "Wow Wow Wow") is a creative way to make your messages stand out and add extra emphasis.</p>
        </div>

        <div class="features">
            <h3 class="features-title">Key Features:</h3>
            <ul class="features-list">
                <li class="features-item">Repeat any text or phrase instantly</li>
                <li class="features-item">Customizable repeat count and separators</li>
                <li class="features-item">Supports newlines, spaces, and custom separators</li>
                <li class="features-item">One-click copy to clipboard</li>
                <li class="features-item">Real-time preview</li>
                <li class="features-item">Mobile-responsive design</li>
                <li class="features-item">No data sent to servers</li>
            </ul>
        </div>

    </div>

    <!-- Copy notification -->
    <div class="text-repeater-notification" id="textRepeaterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Text Repeater Tool
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('textRepeaterInput'),
                output: () => document.getElementById('textRepeaterOutput'),
                notification: () => document.getElementById('textRepeaterNotification'),
                repeatCount: () => document.getElementById('repeatCount'),
                separatorType: () => document.getElementById('separatorType'),
                customSeparator: () => document.getElementById('customSeparator'),
                customSeparatorGroup: () => document.getElementById('customSeparatorGroup')
            };

            window.TextRepeater = {
                repeat() {
                    const input = elements.input();
                    const output = elements.output();
                    const text = input.value;
                    const count = parseInt(elements.repeatCount().value) || 1;

                    if (!text.trim()) {
                        output.textContent = 'Please enter some text to repeat.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    if (count < 1 || count > 1000) {
                        output.textContent = 'Repeat count must be between 1 and 1000.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const separator = this.getSeparator();
                    const repeatedText = this.generateRepeatedText(text, count, separator);
                    output.textContent = repeatedText;
                },

                getSeparator() {
                    const separatorType = elements.separatorType().value;
                    const customSeparator = elements.customSeparator().value;

                    switch (separatorType) {
                        case 'newline': return '\n';
                        case 'space': return ' ';
                        case 'comma': return ',';
                        case 'comma-space': return ', ';
                        case 'semicolon': return ';';
                        case 'pipe': return '|';
                        case 'dash': return '-';
                        case 'custom': return customSeparator || '';
                        default: return '\n';
                    }
                },

                generateRepeatedText(text, count, separator) {
                    const repeatedArray = Array(count).fill(text);
                    return repeatedArray.join(separator);
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your repeated text will appear here...';
                    elements.output().style.color = '';
                    elements.repeatCount().value = '5';
                    elements.separatorType().value = 'newline';
                    elements.customSeparator().value = '';
                    this.toggleCustomSeparator();
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your repeated text will appear here...', 'Please enter some text to repeat.'].includes(text) || text.includes('Repeat count must be')) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                },

                toggleCustomSeparator() {
                    const separatorType = elements.separatorType().value;
                    const customGroup = elements.customSeparatorGroup();
                    customGroup.style.display = separatorType === 'custom' ? 'flex' : 'none';
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                const repeatCount = elements.repeatCount();
                const separatorType = elements.separatorType();
                const customSeparator = elements.customSeparator();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Auto-repeat on input changes
                input.addEventListener('input', function() {
                    if (this.value.trim()) {
                        TextRepeater.repeat();
                    } else {
                        elements.output().textContent = 'Your repeated text will appear here...';
                        elements.output().style.color = '';
                    }
                });

                repeatCount.addEventListener('input', () => {
                    if (input.value.trim()) TextRepeater.repeat();
                });

                separatorType.addEventListener('change', () => {
                    TextRepeater.toggleCustomSeparator();
                    if (input.value.trim()) TextRepeater.repeat();
                });

                customSeparator.addEventListener('input', () => {
                    if (input.value.trim()) TextRepeater.repeat();
                });

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        TextRepeater.repeat();
                    }
                });
            });
        })();
    </script>
</body>
</html>