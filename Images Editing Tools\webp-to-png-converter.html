<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebP to PNG Converter - Free Online Image Format Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free WebP to PNG Converter - Convert Images Online",
        "description": "Convert WebP images to PNG format instantly. Free online tool with lossless quality, transparency preservation, and universal browser compatibility.",
        "url": "https://www.webtoolskit.org/p/webp-to-png.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-22",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "WebP to PNG Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert WebP to PNG" },
            { "@type": "DownloadAction", "name": "Download Converted PNG" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Can WebP be converted to PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, WebP can be easily converted to PNG using our free online converter. The conversion transforms modern WebP images into universally compatible PNG format while preserving transparency and image quality."
          }
        },
        {
          "@type": "Question",
          "name": "Does converting WebP to PNG lose quality?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, converting WebP to PNG doesn't lose quality when done properly. PNG uses lossless compression, so the converted image maintains the same visual quality as the original WebP file, though file size may increase."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert WebP to PNG lossless?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use our online converter which automatically performs lossless WebP to PNG conversion. Upload your WebP file, and the tool converts it to PNG format without any quality degradation, preserving all pixel data and transparency."
          }
        },
        {
          "@type": "Question",
          "name": "Why is PNG being saved as WebP?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Modern browsers and applications often automatically save images as WebP for better compression and faster loading. This happens when websites serve WebP versions or when software defaults to WebP format for efficiency."
          }
        },
        {
          "@type": "Question",
          "name": "What is the main disadvantage of WebP?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The main disadvantage of WebP is limited compatibility with older browsers, software, and systems. While modern browsers support WebP, many legacy applications, image editors, and older devices cannot open or display WebP files."
          }
        }
      ]
    }
    </script>

    <style>
        /* WebP to PNG Widget - Simplified & Template Compatible */
        .webp-png-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .webp-png-widget-container * { box-sizing: border-box; }

        .webp-png-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .webp-png-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .webp-png-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-lg);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
        }

        .webp-png-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .webp-png-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .webp-png-upload-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .webp-png-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .webp-png-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .webp-png-file-input {
            display: none;
        }

        .webp-png-compatibility-info {
            background-color: #e0f2fe;
            border: 1px solid #0288d1;
            color: #01579b;
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-lg);
            font-size: 0.875rem;
            display: none;
        }

        .webp-png-preview {
            display: none;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .webp-png-preview-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .webp-png-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: flex-start;
        }

        .webp-png-preview-item {
            flex: 1;
            text-align: center;
        }

        .webp-png-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .webp-png-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-sm);
        }

        .webp-png-file-info {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .webp-png-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .webp-png-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .webp-png-btn:hover { transform: translateY(-2px); }

        .webp-png-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .webp-png-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .webp-png-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .webp-png-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .webp-png-btn-success {
            background-color: #10b981;
            color: white;
        }

        .webp-png-btn-success:hover {
            background-color: #059669;
        }

        .webp-png-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .webp-png-btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .webp-png-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="png-to-webp"] .webp-png-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .webp-png-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="webp-to-jpg"] .webp-png-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .webp-png-related-tool-item:hover .webp-png-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="png-to-webp"]:hover .webp-png-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .webp-png-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="webp-to-jpg"]:hover .webp-png-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .webp-png-related-tool-item { box-shadow: none; border: none; }
        .webp-png-related-tool-item:hover { box-shadow: none; border: none; }
        .webp-png-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .webp-png-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .webp-png-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .webp-png-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .webp-png-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .webp-png-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .webp-png-related-tool-item:hover .webp-png-related-tool-name { color: var(--primary-color); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .webp-png-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .webp-png-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .webp-png-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .webp-png-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .webp-png-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .webp-png-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .webp-png-widget-title { font-size: 1.875rem; }
            .webp-png-buttons { flex-direction: column; }
            .webp-png-btn { flex: none; }
            .webp-png-preview-content { flex-direction: column; }
            .webp-png-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .webp-png-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .webp-png-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .webp-png-related-tool-name { font-size: 0.875rem; }
            .webp-png-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .webp-png-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .webp-png-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .webp-png-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .webp-png-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .webp-png-upload-area:hover { background-color: var(--card-bg); }
        .webp-png-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        [data-theme="dark"] .webp-png-compatibility-info { background-color: #0d47a1; border-color: #1976d2; color: #bbdefb; }
    </style>
</head>
<body>
    <div class="webp-png-widget-container">
        <h1 class="webp-png-widget-title">WebP to PNG Converter</h1>
        <p class="webp-png-widget-description">
            Convert WebP images to PNG format for universal compatibility. Preserve transparency and quality while ensuring support across all browsers and applications.
        </p>
        
        <div class="webp-png-upload-area" id="uploadArea">
            <div class="webp-png-upload-icon">📁</div>
            <div class="webp-png-upload-text">Click to select WebP image or drag & drop</div>
            <div class="webp-png-upload-subtext">Supports WebP files (Max 10MB)</div>
            <input type="file" id="fileInput" class="webp-png-file-input" accept=".webp">
        </div>

        <div class="webp-png-compatibility-info" id="compatibilityInfo">
            <strong>Compatibility Boost:</strong> Converting to PNG ensures your images work in all browsers, image editors, and legacy systems that don't support WebP format.
        </div>

        <div class="webp-png-preview" id="previewSection">
            <h3 class="webp-png-preview-title">Preview & Comparison</h3>
            <div class="webp-png-preview-content">
                <div class="webp-png-preview-item">
                    <div class="webp-png-preview-label">Original WebP</div>
                    <img id="originalImage" class="webp-png-preview-image" alt="Original WebP" />
                    <div class="webp-png-file-info" id="originalInfo"></div>
                </div>
                <div class="webp-png-preview-item">
                    <div class="webp-png-preview-label">Converted PNG</div>
                    <img id="convertedImage" class="webp-png-preview-image" alt="Converted PNG" />
                    <div class="webp-png-file-info" id="convertedInfo"></div>
                </div>
            </div>
        </div>

        <div class="webp-png-buttons">
            <button id="convertBtn" class="webp-png-btn webp-png-btn-primary" disabled>
                Convert to PNG
            </button>
            <button id="downloadBtn" class="webp-png-btn webp-png-btn-success" disabled>
                Download PNG
            </button>
            <button id="resetBtn" class="webp-png-btn webp-png-btn-secondary">
                Reset
            </button>
        </div>

        <div class="webp-png-related-tools">
            <h3 class="webp-png-related-tools-title">Related Tools</h3>
            <div class="webp-png-related-tools-grid">
                <a href="https://www.webtoolskit.org/p/png-to-webp.html" class="webp-png-related-tool-item" rel="noopener">
                    <div class="webp-png-related-tool-icon">
                        <i class="fas fa-undo-alt"></i>
                    </div>
                    <div class="webp-png-related-tool-name">PNG to WebP</div>
                </a>

                <a href="https://www.webtoolskit.org/p/image-converter_23.html" class="webp-png-related-tool-item" rel="noopener">
                    <div class="webp-png-related-tool-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="webp-png-related-tool-name">Image Converter</div>
                </a>

                <a href="https://www.webtoolskit.org/p/webp-to-jpg.html" class="webp-png-related-tool-item" rel="noopener">
                    <div class="webp-png-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="webp-png-related-tool-name">WebP to JPG</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert WebP to PNG Online - Universal Compatibility</h2>
            <p>Our <strong>WebP to PNG Converter</strong> transforms modern WebP images into universally compatible PNG format, ensuring your images work across all browsers, applications, and devices. While WebP offers excellent compression, PNG provides unmatched compatibility and lossless quality.</p>

            <p>Converting WebP to PNG is essential when you need maximum compatibility with older browsers, image editing software, or legacy systems. Our tool preserves transparency and image quality while creating PNG files that work everywhere.</p>

            <h3>Frequently Asked Questions About WebP to PNG Conversion</h3>

            <h4>Can WebP be converted to PNG?</h4>
            <p>Yes, WebP can be easily converted to PNG using our free online converter. The conversion transforms modern WebP images into universally compatible PNG format while preserving transparency and image quality.</p>

            <h4>Does converting WebP to PNG lose quality?</h4>
            <p>No, converting WebP to PNG doesn't lose quality when done properly. PNG uses lossless compression, so the converted image maintains the same visual quality as the original WebP file, though file size may increase.</p>

            <h4>How to convert WebP to PNG lossless?</h4>
            <p>Use our online converter which automatically performs lossless WebP to PNG conversion. Upload your WebP file, and the tool converts it to PNG format without any quality degradation, preserving all pixel data and transparency.</p>

            <h4>Why is PNG being saved as WebP?</h4>
            <p>Modern browsers and applications often automatically save images as WebP for better compression and faster loading. This happens when websites serve WebP versions or when software defaults to WebP format for efficiency.</p>

            <h4>What is the main disadvantage of WebP?</h4>
            <p>The main disadvantage of WebP is limited compatibility with older browsers, software, and systems. While modern browsers support WebP, many legacy applications, image editors, and older devices cannot open or display WebP files.</p>
        </div>

        <div class="webp-png-features">
            <h3 class="webp-png-features-title">Key Features</h3>
            <ul class="webp-png-features-list">
                <li class="webp-png-features-item">Convert WebP to PNG instantly</li>
                <li class="webp-png-features-item">Lossless quality preservation</li>
                <li class="webp-png-features-item">Transparency support</li>
                <li class="webp-png-features-item">Universal browser compatibility</li>
                <li class="webp-png-features-item">Client-side processing for privacy</li>
                <li class="webp-png-features-item">Legacy system support</li>
                <li class="webp-png-features-item">Real-time preview</li>
                <li class="webp-png-features-item">Professional image quality</li>
            </ul>
        </div>
    </div>

    <script>
        // WebP to PNG Converter Tool - Self-contained IIFE
        (function() {
            'use strict';

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                compatibilityInfo: () => document.getElementById('compatibilityInfo'),
                previewSection: () => document.getElementById('previewSection'),
                originalImage: () => document.getElementById('originalImage'),
                convertedImage: () => document.getElementById('convertedImage'),
                originalInfo: () => document.getElementById('originalInfo'),
                convertedInfo: () => document.getElementById('convertedInfo'),
                convertBtn: () => document.getElementById('convertBtn'),
                downloadBtn: () => document.getElementById('downloadBtn'),
                resetBtn: () => document.getElementById('resetBtn')
            };

            let originalFile = null;
            let convertedBlob = null;

            function init() {
                setupEventListeners();
            }

            function setupEventListeners() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();
                const convertBtn = elements.convertBtn();
                const downloadBtn = elements.downloadBtn();
                const resetBtn = elements.resetBtn();

                // File upload events
                uploadArea.addEventListener('click', () => fileInput.click());
                fileInput.addEventListener('change', handleFileSelect);

                // Drag and drop events
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('drop', handleDrop);

                // Button events
                convertBtn.addEventListener('click', convertImage);
                downloadBtn.addEventListener('click', downloadImage);
                resetBtn.addEventListener('click', resetTool);
            }

            function handleFileSelect(event) {
                const file = event.target.files[0];
                if (file) processFile(file);
            }

            function handleDragOver(event) {
                event.preventDefault();
                elements.uploadArea().classList.add('dragover');
            }

            function handleDragLeave(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
            }

            function handleDrop(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
                const files = event.dataTransfer.files;
                if (files.length > 0) processFile(files[0]);
            }

            function processFile(file) {
                if (!file.type.includes('webp')) {
                    alert('Please select a WebP image file.');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB.');
                    return;
                }

                originalFile = file;
                displayOriginalImage();
                elements.convertBtn().disabled = false;
                elements.compatibilityInfo().style.display = 'block';
            }

            function displayOriginalImage() {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const originalImage = elements.originalImage();
                    originalImage.src = e.target.result;
                    
                    const originalInfo = elements.originalInfo();
                    originalInfo.textContent = `${originalFile.name} (${formatFileSize(originalFile.size)})`;
                    
                    elements.previewSection().style.display = 'block';
                };
                reader.readAsDataURL(originalFile);
            }

            function convertImage() {
                if (!originalFile) return;

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    // Convert to PNG format (lossless)
                    canvas.toBlob((blob) => {
                        convertedBlob = blob;
                        displayConvertedImage();
                        elements.downloadBtn().disabled = false;
                    }, 'image/png');
                };

                img.src = URL.createObjectURL(originalFile);
            }

            function displayConvertedImage() {
                const convertedImage = elements.convertedImage();
                convertedImage.src = URL.createObjectURL(convertedBlob);
                
                const convertedInfo = elements.convertedInfo();
                const fileName = originalFile.name.replace(/\.[^/.]+$/, '') + '.png';
                const sizeChange = convertedBlob.size > originalFile.size ? 'increase' : 'decrease';
                const sizeRatio = ((convertedBlob.size / originalFile.size) * 100).toFixed(1);
                convertedInfo.innerHTML = `${fileName} (${formatFileSize(convertedBlob.size)})<br><small style="color: ${sizeChange === 'increase' ? '#dc3545' : '#28a745'};">Size ${sizeChange}: ${sizeRatio}%</small>`;
            }

            function downloadImage() {
                if (!convertedBlob) return;

                const link = document.createElement('a');
                link.href = URL.createObjectURL(convertedBlob);
                link.download = originalFile.name.replace(/\.[^/.]+$/, '') + '.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            function resetTool() {
                originalFile = null;
                convertedBlob = null;
                elements.fileInput().value = '';
                elements.previewSection().style.display = 'none';
                elements.compatibilityInfo().style.display = 'none';
                elements.convertBtn().disabled = true;
                elements.downloadBtn().disabled = true;
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', init);
        })();
    </script>
</body>
</html>