<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text to Slug Converter Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Text to Slug Converter - Create SEO-Friendly URLs",
        "description": "Convert any text into SEO-friendly URL slugs instantly. Free online tool with customizable options, real-time conversion, and one-click copying.",
        "url": "https://www.webtoolskit.org/p/text-to-slug_30.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Text to Slug Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Text to Slug" },
            { "@type": "CopyAction", "name": "Copy Generated Slug" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is text to slug?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Text to slug is the process of converting a string of text, like a page title, into a URL-friendly format. This typically involves making the text lowercase, removing special characters, and replacing spaces with hyphens to create a clean and readable URL path."
          }
        },
        {
          "@type": "Question",
          "name": "How to create a slug?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The easiest way to create a slug is with an online converter. Paste your text (like a blog title) into the tool, and it will automatically generate a properly formatted, SEO-friendly slug for you to copy and paste into your website's CMS."
          }
        },
        {
          "@type": "Question",
          "name": "What is an example of a URL slug?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "For a blog post titled 'The 10 Best Tips for SEO!', a good URL slug would be '10-best-tips-for-seo'. The full URL might look like: https://example.com/blog/10-best-tips-for-seo, where '10-best-tips-for-seo' is the slug."
          }
        },
        {
          "@type": "Question",
          "name": "What is a slug and what does it do?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A slug is the part of a URL that identifies a specific page in a human-readable way. It serves two main purposes: it helps search engines understand the page's content by including relevant keywords, and it improves user experience by making URLs clear and easy to understand."
          }
        },
        {
          "@type": "Question",
          "name": "Does changing the slug change the URL?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, changing a page's slug will change its URL. If you change the slug of a published page, you must set up a 301 redirect from the old URL to the new one. This is crucial to avoid broken links and to pass any existing SEO authority to the new URL."
          }
        }
      ]
    }
    </script>


    <style>
        /* Text to Slug Widget - Simplified & Template Compatible */
        .text-slug-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .text-slug-widget-container * { box-sizing: border-box; }

        .text-slug-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-slug-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .text-slug-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .text-slug-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .text-slug-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .text-slug-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .text-slug-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .text-slug-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .text-slug-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .text-slug-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .text-slug-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .text-slug-btn:hover { transform: translateY(-2px); }

        .text-slug-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .text-slug-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .text-slug-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .text-slug-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .text-slug-btn-success {
            background-color: #10b981;
            color: white;
        }

        .text-slug-btn-success:hover {
            background-color: #059669;
        }

        .text-slug-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .text-slug-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-slug-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-all;
            min-height: 60px;
            color: var(--text-color);
            line-height: 1.5;
        }

        .text-slug-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .text-slug-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .text-slug-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .text-slug-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-slug-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .text-slug-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .text-slug-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .text-slug-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="case-converter"] .text-slug-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="word-counter"] .text-slug-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="remove-line-breaks"] .text-slug-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .text-slug-related-tool-item:hover .text-slug-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="case-converter"]:hover .text-slug-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="word-counter"]:hover .text-slug-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="remove-line-breaks"]:hover .text-slug-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .text-slug-related-tool-item { box-shadow: none; border: none; }
        .text-slug-related-tool-item:hover { box-shadow: none; border: none; }
        .text-slug-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .text-slug-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .text-slug-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .text-slug-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .text-slug-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .text-slug-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .text-slug-related-tool-item:hover .text-slug-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .text-slug-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .text-slug-widget-title { font-size: 1.875rem; }
            .text-slug-buttons { flex-direction: column; }
            .text-slug-btn { flex: none; }
            .text-slug-options { grid-template-columns: 1fr; }
            .text-slug-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .text-slug-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .text-slug-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .text-slug-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .text-slug-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .text-slug-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .text-slug-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .text-slug-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .text-slug-textarea:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .text-slug-checkbox:focus, .text-slug-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .text-slug-output::selection { background-color: var(--primary-color); color: white; }
        .text-slug-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .text-slug-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="text-slug-widget-container">
        <h1 class="text-slug-widget-title">Text to Slug Converter</h1>
        <p class="text-slug-widget-description">
            Transform any text into SEO-friendly, URL-safe slugs instantly. Perfect for creating clean URLs, file names, and identifiers.
        </p>
        
        <div class="text-slug-input-group">
            <label for="textSlugInput" class="text-slug-label">Enter your text:</label>
            <textarea 
                id="textSlugInput" 
                class="text-slug-textarea"
                placeholder="Type or paste your text here to convert it to a slug..."
                rows="4"
            ></textarea>
        </div>

        <div class="text-slug-options">
            <div class="text-slug-option">
                <input type="checkbox" id="slugLowercase" class="text-slug-checkbox" checked>
                <label for="slugLowercase" class="text-slug-option-label">Convert to lowercase</label>
            </div>
            <div class="text-slug-option">
                <input type="checkbox" id="slugRemoveSpecial" class="text-slug-checkbox" checked>
                <label for="slugRemoveSpecial" class="text-slug-option-label">Remove special characters</label>
            </div>
            <div class="text-slug-option">
                <input type="checkbox" id="slugTrimSpaces" class="text-slug-checkbox" checked>
                <label for="slugTrimSpaces" class="text-slug-option-label">Trim extra spaces</label>
            </div>
            <div class="text-slug-option">
                <input type="checkbox" id="slugRemoveNumbers" class="text-slug-checkbox">
                <label for="slugRemoveNumbers" class="text-slug-option-label">Remove numbers</label>
            </div>
        </div>

        <div class="text-slug-buttons">
            <button class="text-slug-btn text-slug-btn-primary" onclick="TextSlugConverter.convert()">
                Convert to Slug
            </button>
            <button class="text-slug-btn text-slug-btn-secondary" onclick="TextSlugConverter.clear()">
                Clear All
            </button>
            <button class="text-slug-btn text-slug-btn-success" onclick="TextSlugConverter.copy()">
                Copy Result
            </button>
        </div>

        <div class="text-slug-result">
            <h3 class="text-slug-result-title">Generated Slug:</h3>
            <div class="text-slug-output" id="textSlugOutput">
                Your converted slug will appear here...
            </div>
        </div>

        <div class="text-slug-related-tools">
            <h3 class="text-slug-related-tools-title">Related Tools</h3>
            <div class="text-slug-related-tools-grid">
                <a href="/p/case-converter.html" class="text-slug-related-tool-item" rel="noopener">
                    <div class="text-slug-related-tool-icon">
                        <i class="fas fa-text-height"></i>
                    </div>
                    <div class="text-slug-related-tool-name">Case Converter</div>
                </a>

                <a href="/p/word-counter.html" class="text-slug-related-tool-item" rel="noopener">
                    <div class="text-slug-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="text-slug-related-tool-name">Word Counter</div>
                </a>

                <a href="/p/remove-line-breaks.html" class="text-slug-related-tool-item" rel="noopener">
                    <div class="text-slug-related-tool-icon">
                        <i class="fas fa-align-left"></i>
                    </div>
                    <div class="text-slug-related-tool-name">Remove Line Breaks</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Optimize Your URLs with Our Text to Slug Converter</h2>
            <p>A URL slug is the part of a web address that identifies a specific page in a clean, human-readable format. Creating a good slug is essential for search engine optimization (SEO) and user experience. A well-crafted slug should be short, descriptive, and include relevant keywords. Our <strong>Text to Slug</strong> converter automates this process, transforming any text, like a blog post title, into a perfect, SEO-friendly URL slug instantly.</p>
            <p>Instead of manually removing special characters, converting to lowercase, and replacing spaces with hyphens, this tool does it all for you. This ensures your URLs are not only professional but also optimized to rank higher in search results and be more appealing to users.</p>
            
            <h3>How to Create an SEO-Friendly Slug</h3>
            <ol>
                <li><strong>Enter Your Text:</strong> Type or paste your title or phrase into the input box.</li>
                <li><strong>Customize Options:</strong> Use the checkboxes to decide if you want to convert to lowercase, remove special characters, or trim spaces. By default, all standard options are enabled.</li>
                <li><strong>Convert and Copy:</strong> The tool will generate the slug in real-time. Once ready, click the "Copy Result" button to use it in your website's CMS.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Text to Slug</h3>
            
            <h4>What is text to slug?</h4>
            <p>Text to slug is the process of converting a regular string of text (like a title) into a URL-friendly format. This involves making the text lowercase, stripping out special characters (e.g., !, ?, &), and replacing spaces with hyphens. The goal is to create a clean and readable identifier for a webpage.</p>
            
            <h4>How to create a slug?</h4>
            <p>Using an online converter is the most reliable way. Just paste your text into the tool, and it will automatically handle the lowercasing, character removal, and hyphen replacement. This prevents errors and ensures your slug is properly formatted for the web.</p>
            
            <h4>What is an example of a URL slug?</h4>
            <p>If your article title is "The Best Ways to Learn Coding in 2024!", a good URL slug would be <code>best-ways-to-learn-coding-in-2024</code>. It's concise, descriptive, and easy for both users and search engines to read.</p>
            
            <h4>What is a slug and what does it do?</h4>
            <p>A slug is the part of a URL that comes after the domain name and identifies the specific page content. It plays a crucial role in SEO by allowing you to include keywords directly in the URL, which helps search engines understand the page's topic. It also improves usability by making links intuitive and easy to remember.</p>
            
            <h4>Does changing the slug change the URL?</h4>
            <p>Yes, absolutely. The slug is a fundamental part of the URL. If you change the slug of a page that is already live, you are creating a new URL. It is critical to implement a 301 redirect from the old URL to the new one to preserve your SEO rankings and prevent users from encountering a "404 Not Found" error.</p>
        </div>


        <div class="text-slug-features">
            <h3 class="text-slug-features-title">Key Features:</h3>
            <ul class="text-slug-features-list">
                <li class="text-slug-features-item" style="margin-bottom: 0.3em;">Instant text-to-slug conversion</li>
                <li class="text-slug-features-item" style="margin-bottom: 0.3em;">Customizable conversion options</li>
                <li class="text-slug-features-item" style="margin-bottom: 0.3em;">SEO-friendly URL generation</li>
                <li class="text-slug-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="text-slug-features-item" style="margin-bottom: 0.3em;">Real-time preview</li>
                <li class="text-slug-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="text-slug-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="text-slug-notification" id="textSlugNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Simplified Text to Slug Converter
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('textSlugInput'),
                output: () => document.getElementById('textSlugOutput'),
                notification: () => document.getElementById('textSlugNotification')
            };

            window.TextSlugConverter = {
                convert() {
                    const input = elements.input();
                    const output = elements.output();
                    const text = input.value;

                    if (!text.trim()) {
                        output.textContent = 'Please enter some text to convert.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const options = {
                        lowercase: document.getElementById('slugLowercase').checked,
                        removeSpecial: document.getElementById('slugRemoveSpecial').checked,
                        trimSpaces: document.getElementById('slugTrimSpaces').checked,
                        removeNumbers: document.getElementById('slugRemoveNumbers').checked
                    };

                    const slug = this.processText(text, options);
                    output.textContent = slug || 'No valid characters found';
                },

                processText(text, options) {
                    let result = text;
                    if (options.lowercase) result = result.toLowerCase();
                    if (options.removeNumbers) result = result.replace(/\d+/g, '');
                    if (options.removeSpecial) result = result.replace(/[^\w\s-]/g, '');
                    if (options.trimSpaces) result = result.replace(/\s+/g, ' ').trim();

                    return result.replace(/\s+/g, '-').replace(/-+/g, '-').replace(/^-+|-+$/g, '');
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your converted slug will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your converted slug will appear here...', 'Please enter some text to convert.', 'No valid characters found'].includes(text)) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                const checkboxes = document.querySelectorAll('.text-slug-checkbox');

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Auto-convert on input
                input.addEventListener('input', function() {
                    if (this.value.trim()) {
                        TextSlugConverter.convert();
                    } else {
                        elements.output().textContent = 'Your converted slug will appear here...';
                        elements.output().style.color = '';
                    }
                });

                // Auto-convert when options change
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', () => {
                        if (input.value.trim()) TextSlugConverter.convert();
                    });
                });

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        TextSlugConverter.convert();
                    }
                });
            });
        })();
    </script>
</body>
</html>