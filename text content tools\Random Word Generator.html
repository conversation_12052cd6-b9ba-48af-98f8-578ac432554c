<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Random Word Generator Widget</title>
    
    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Random Word Generator - Generate Random Words Instantly",
        "description": "Generate random words for creative writing, brainstorming, and games. Free online tool with customizable word count, categories, and output formats.",
        "url": "https://www.webtoolskit.org/p/random-word-generator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-17",
        "dateModified": "2025-06-17",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Random Word Generator",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Generate Random Words" },
            { "@type": "CopyAction", "name": "Copy Generated Words" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a random word generator used for?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A random word generator is used for a variety of creative and practical purposes, including: overcoming writer's block, creating writing prompts, brainstorming ideas for stories or projects, playing drawing or guessing games like Pictionary, building vocabulary, and helping to name characters or products."
          }
        },
        {
          "@type": "Question",
          "name": "How do you generate random words for writing prompts?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To generate writing prompts, set the number of words to 3-5, choose a category like 'nouns' or 'adjectives', and click 'Generate Words'. Use the resulting words as the central theme, characters, or objects in a new story, poem, or scene. For example, the words 'mountain', 'clock', and 'whisper' could inspire a complete story."
          }
        },
        {
          "@type": "Question",
          "name": "Can a random word generator help improve creativity?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, absolutely. A random word generator can significantly boost creativity by forcing your brain to connect unrelated concepts. This practice, known as lateral thinking, helps break conventional thought patterns and can lead to highly original and unique ideas."
          }
        },
        {
          "@type": "Question",
          "name": "Is there a random word generator with filters?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, this tool acts as a random word generator with filters. You can use the 'Word category' filter to generate specific types of words, such as only nouns, verbs, or adjectives, making the results more relevant to your specific task."
          }
        },
        {
          "@type": "Question",
          "name": "How many words can a random word generator produce?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our random word generator allows you to produce between 1 and 100 words at a time. This range is flexible enough for most applications, whether you need a single word for inspiration or a long list for a classroom activity or game."
          }
        }
      ]
    }
    </script>

    <style>
        /* Random Word Generator Widget - Simplified & Template Compatible */
        .random-word-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .random-word-widget-container * { box-sizing: border-box; }

        .random-word-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .random-word-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .random-word-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .random-word-option {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .random-word-label {
            font-weight: 600;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .random-word-input, .random-word-select {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-family: var(--font-family);
        }

        .random-word-input:focus, .random-word-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .random-word-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .random-word-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .random-word-btn:hover { transform: translateY(-2px); }

        .random-word-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .random-word-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .random-word-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .random-word-btn-secondary:hover { background-color: var(--border-color); }

        .random-word-btn-success {
            background-color: #10b981;
            color: white;
        }

        .random-word-btn-success:hover { background-color: #059669; }

        .random-word-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .random-word-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .random-word-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: var(--font-size-base);
            min-height: 150px;
            color: var(--text-color);
            line-height: 1.8;
            word-wrap: break-word;
        }

        .random-word {
            display: inline-block;
            margin: var(--spacing-xs) var(--spacing-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: var(--primary-color);
            color: white;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            font-size: 0.95rem;
        }

        .random-word-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .random-word-notification.show { transform: translateX(0); }
        
        /* SEO Content Section Styles */
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        
        /* === START: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        .related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="lorem-ipsum-generator"] .related-tool-icon { background: linear-gradient(145deg, #8B5CF6, #7C3AED); }
        a[href*="word-counter"] .related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }
        a[href*="case-converter"] .related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }

        .related-tool-item:hover .related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        a[href*="lorem-ipsum-generator"]:hover .related-tool-icon { background: linear-gradient(145deg, #a78bfa, #8B5CF6); }
        a[href*="word-counter"]:hover .related-tool-icon { background: linear-gradient(145deg, #14b8a6, #10B981); }
        a[href*="case-converter"]:hover .related-tool-icon { background: linear-gradient(145deg, #f472b6, #EC4899); }

        .related-tool-item {
            box-shadow: none; border: none; text-align: center; text-decoration: none; color: inherit;
            transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg);
            display: block; width: 100%; max-width: 160px;
        }

        .related-tool-item:hover { transform: translateY(0); background-color: transparent; box-shadow: none; border: none; }
        .related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .related-tool-item:hover .related-tool-name { color: var(--primary-color); }
        
        /* === END: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        /* === START: STANDARDIZED FEATURES SECTION === */
        .features { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .features-title { color: var(--text-color); margin-bottom: var(--spacing-md); font-size: 1.25rem; font-weight: 700; }
        .features-list { list-style: none; padding: 0; margin: 0; columns: 2; -webkit-columns: 2; -moz-columns: 2; }
        .features-item { padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg); color: var(--text-color-light); position: relative; margin-bottom: 0.3em; }
        .features-item:before { content: ""; position: absolute; left: 0; top: calc(var(--spacing-sm) + 2px); width: 12px; height: 6px; border-left: 2px solid #10b981; border-bottom: 2px solid #10b981; transform: rotate(-45deg); }
        
        @media (max-width: 600px) { .features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
        /* === END: STANDARDIZED FEATURES SECTION === */

        /* Responsive Design */
        @media (max-width: 768px) {
            .random-word-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .random-word-widget-title { font-size: 1.875rem; }
            .random-word-buttons { flex-direction: column; }
            .random-word-btn { flex: none; }
            .random-word-options { grid-template-columns: 1fr; }
            .related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .related-tool-item { padding: var(--spacing-md); max-width: none; }
            .related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .related-tool-name { font-size: 0.875rem; }
        }
        
        @media (max-width: 480px) {
            .related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .related-tool-name { font-size: 0.75rem; }
        }

        /* Dark Mode Support */
        [data-theme="dark"] .random-word-input:focus,
        [data-theme="dark"] .random-word-select:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        /* Focus & Accessibility */
        .random-word-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .random-word-output::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="random-word-widget-container">
        <h1 class="random-word-widget-title">Random Word Generator</h1>
        <p class="random-word-widget-description">
            Generate random words for creative writing, brainstorming, games, and more. Customize word count and categories to spark your creativity.
        </p>

        <div class="random-word-options">
            <div class="random-word-option">
                <label for="count" class="random-word-label">Number of words:</label>
                <input type="number" id="count" class="random-word-input" value="10" min="1" max="100">
            </div>
            <div class="random-word-option">
                <label for="category" class="random-word-label">Word category:</label>
                <select id="category" class="random-word-select">
                    <option value="all">All words</option>
                    <option value="nouns">Nouns</option>
                    <option value="adjectives">Adjectives</option>
                    <option value="verbs">Verbs</option>
                </select>
            </div>
            <div class="random-word-option">
                <label for="format" class="random-word-label">Output format:</label>
                <select id="format" class="random-word-select">
                    <option value="badges">Word badges</option>
                    <option value="list">Simple list</option>
                    <option value="comma">Comma separated</option>
                </select>
            </div>
        </div>

        <div class="random-word-buttons">
            <button class="random-word-btn random-word-btn-primary" onclick="RandomWordGenerator.generate()">Generate Words</button>
            <button class="random-word-btn random-word-btn-secondary" onclick="RandomWordGenerator.clear()">Clear All</button>
            <button class="random-word-btn random-word-btn-success" onclick="RandomWordGenerator.copy()">Copy Words</button>
        </div>

        <div class="random-word-result">
            <h3 class="random-word-result-title">Generated Words:</h3>
            <div class="random-word-output" id="randomWordOutput">Your random words will appear here...</div>
        </div>
        
        <div class="related-tools">
            <h3 class="related-tools-title">Related Tools</h3>
            <div class="related-tools-grid">
                <a href="/p/lorem-ipsum-generator.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-align-left"></i>
                    </div>
                    <div class="related-tool-name">Lorem Ipsum Generator</div>
                </a>

                <a href="/p/word-counter.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="related-tool-name">Word Counter</div>
                </a>

                <a href="/p/case-converter.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-text-height"></i>
                    </div>
                    <div class="related-tool-name">Case Converter</div>
                </a>
            </div>
        </div>
        
        <div class="seo-content">
            <h2>Spark Creativity with Our Random Word Generator</h2>
            <p>A <strong>Random Word Generator</strong> is a powerful creative tool designed to help you break out of mental ruts and discover new ideas. Whether you're a writer facing a blank page, a teacher looking for classroom activities, or an artist seeking inspiration, this tool provides a simple way to generate instant, random words. By presenting unexpected combinations, it encourages lateral thinking and can be the catalyst for your next great story, project, or design.</p>
            
            <h3>How to Use the Random Word Generator</h3>
            <p>Our tool is designed for flexibility and ease of use. Follow these simple steps:</p>
            <ol>
                <li><strong>Choose the Number of Words:</strong> Enter how many random words you want to generate (from 1 to 100).</li>
                <li><strong>Select a Category:</strong> Use the filter to get specific types of words. You can choose from nouns, verbs, adjectives, or all words combined.</li>
                <li><strong>Pick an Output Format:</strong> Decide how you want the words displayed—as stylish "Word badges," a "Simple list," or a "Comma separated" string.</li>
                <li><strong>Generate and Copy:</strong> Click the "Generate Words" button to see your results. Use the "Copy Words" button to save them to your clipboard.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Random Word Generation</h3>
            
            <h4>What is a random word generator used for?</h4>
            <p>This tool is incredibly versatile. It's used for brainstorming, creating unique writing prompts, playing word games like Pictionary, building vocabulary for language learners, generating ideas for art projects, and even helping developers find placeholder or variable names. It's a go-to resource for anyone needing a spark of random inspiration.</p>
            
            <h4>How do you generate random words for writing prompts?</h4>
            <p>For an effective writing prompt, try generating 3 to 5 words. You can mix categories, such as two nouns and one verb (e.g., "castle," "river," "whispers"), to create a compelling scenario. The goal is to use the generated words as the core elements of a new story, poem, or scene.</p>
            
            <h4>Can a random word generator help improve creativity?</h4>
            <p>Absolutely. Creativity often thrives on making new connections between existing ideas. A random word generator forces your brain to link concepts you wouldn't normally associate with one another. This exercise strengthens your creative muscles and helps you produce more original and unexpected work.</p>
            
            <h4>Is there a random word generator with filters?</h4>
            <p>Yes, this tool includes powerful filtering options. You can use the "Word category" dropdown to filter the results to only show nouns, adjectives, or verbs. This allows you to tailor the output to your specific needs, whether you're describing a character (adjectives) or outlining a plot (verbs).</p>
            
            <h4>How many words can a random word generator produce?</h4>
            <p>Our generator can produce anywhere from 1 to 100 words in a single request. This flexible range makes it suitable for a wide variety of tasks, from finding a single, powerful word to generating a large list for educational purposes or games.</p>
        </div>

        <div class="features">
            <h3 class="features-title">Key Features:</h3>
            <ul class="features-list">
                <li class="features-item">Instant random word generation</li>
                <li class="features-item">Customizable word count and categories</li>
                <li class="features-item">Multiple output formats available</li>
                <li class="features-item">One-click copy to clipboard</li>
                <li class="features-item">Real-time word generation</li>
                <li class="features-item">Mobile-responsive design</li>
                <li class="features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="random-word-notification" id="randomWordNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Simplified Random Word Generator
        (function() {
            'use strict';

            const elements = {
                output: () => document.getElementById('randomWordOutput'),
                notification: () => document.getElementById('randomWordNotification')
            };

            const wordLists = {
                nouns: ['apple','book','car','dog','elephant','flower','guitar','house','island','jacket','kite','lamp','mountain','notebook','ocean','piano','queen','river','star','tree','umbrella','village','window','xylophone','yacht','zebra','bridge','castle','diamond','engine','forest','garden','hammer','igloo','jungle','kitchen','library','mirror','needle','orange','pencil','quilt','robot','sandwich','table','unicorn','volcano','wallet','xerox','yogurt','zipper'],
                adjectives: ['amazing','bright','creative','delicious','elegant','funny','gorgeous','happy','incredible','joyful','kind','lovely','magnificent','nice','outstanding','perfect','quiet','radiant','stunning','terrific','unique','vibrant','wonderful','excellent','youthful','zealous','beautiful','charming','dazzling','energetic','fantastic','graceful','harmonious','inspiring','jubilant','keen','luminous','marvelous','noble','optimistic','peaceful','quirky','remarkable','splendid','tremendous'],
                verbs: ['run','jump','sing','dance','write','read','cook','paint','swim','fly','laugh','cry','think','dream','explore','create','build','destroy','love','hate','help','learn','teach','play','work','rest','sleep','wake','eat','drink','walk','talk','listen','watch','see','hear','feel','touch','smell','taste','remember','forget','hope','fear','believe','doubt','trust','betray','forgive','celebrate','mourn','succeed','fail','try','quit','start','finish','begin','end','open','close']
            };

            const allWords = [...wordLists.nouns, ...wordLists.adjectives, ...wordLists.verbs];

            window.RandomWordGenerator = {
                generate() {
                    const count = parseInt(document.getElementById('count').value) || 10;
                    const category = document.getElementById('category').value;
                    const format = document.getElementById('format').value;
                    const output = elements.output();

                    if (count < 1 || count > 100) {
                        output.textContent = 'Please enter a number between 1 and 100.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';

                    // Get word list based on category
                    let words = category === 'all' ? allWords : wordLists[category] || allWords;

                    // Generate random words
                    const selectedWords = [];
                    for (let i = 0; i < count; i++) {
                        const randomIndex = Math.floor(Math.random() * words.length);
                        selectedWords.push(words[randomIndex]);
                    }

                    // Format output
                    this.displayWords(selectedWords, format, output);
                },

                displayWords(words, format, output) {
                    if (format === 'badges') {
                        output.innerHTML = words.map(word => `<span class="random-word">${word}</span>`).join('');
                    } else if (format === 'list') {
                        output.textContent = words.map((word, index) => `${index + 1}. ${word}`).join('\n');
                    } else if (format === 'comma') {
                        output.textContent = words.join(', ');
                    }
                },

                clear() {
                    elements.output().textContent = 'Your random words will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const output = elements.output();
                    let text = '';

                    if (output.textContent === 'Your random words will appear here...' ||
                        output.textContent === 'Please enter a number between 1 and 100.') return;

                    // Extract text based on format
                    if (output.innerHTML.includes('<span class="random-word">')) {
                        // Badge format - extract text from spans
                        const words = Array.from(output.querySelectorAll('.random-word')).map(span => span.textContent);
                        text = words.join(', ');
                    } else {
                        text = output.textContent;
                    }

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Auto-generate on option changes
                ['count', 'category', 'format'].forEach(id => {
                    document.getElementById(id).addEventListener('change', () => {
                        if (document.getElementById('count').value.trim()) {
                            RandomWordGenerator.generate();
                        }
                    });
                });

                // Generate initial words
                RandomWordGenerator.generate();
            });
        })();
    </script>
</body>
</html>