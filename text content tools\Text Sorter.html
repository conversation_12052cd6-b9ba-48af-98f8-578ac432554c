<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Sorter Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Text Sorter - Sort Lines Alphabetically & Numerically",
        "description": "Sort lines of text alphabetically, numerically, by length, or in reverse order. Free online tool with multiple sorting options and instant results.",
        "url": "https://www.webtoolskit.org/p/text-sorter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Text Sorter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "OrganizeAction", "name": "Sort Text Lines" },
            { "@type": "CopyAction", "name": "Copy Sorted Text" }
        ]
    }
    </script>

    <style>
        /* Text Sorter Widget - Simplified & Template Compatible */
        .text-sorter-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .text-sorter-widget-container * { box-sizing: border-box; }

        .text-sorter-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-sorter-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .text-sorter-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .text-sorter-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 150px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .text-sorter-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .text-sorter-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .text-sorter-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .text-sorter-radio {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .text-sorter-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .text-sorter-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .text-sorter-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .text-sorter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .text-sorter-btn:hover { transform: translateY(-2px); }

        .text-sorter-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .text-sorter-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .text-sorter-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .text-sorter-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .text-sorter-btn-success {
            background-color: #10b981;
            color: white;
        }

        .text-sorter-btn-success:hover {
            background-color: #059669;
        }

        .text-sorter-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .text-sorter-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .text-sorter-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 150px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .text-sorter-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .text-sorter-notification.show { transform: translateX(0); }

        /* === START: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        .related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="text-to-slug"] .related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="text-repeater"] .related-tool-icon { background: linear-gradient(145deg, #14B8A6, #0D9488); }
        a[href*="comma-separator"] .related-tool-icon { background: linear-gradient(145deg, #F97316, #EA580C); }

        .related-tool-item:hover .related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .related-tool-item {
            box-shadow: none;
            border: none;
            text-align: center;
            text-decoration: none;
            color: inherit;
            transition: var(--transition-base);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            display: block;
            width: 100%;
            max-width: 160px;
        }
        
        .related-tool-item:hover {
            transform: translateY(0);
            background-color: transparent;
        }
        
        .related-tools {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }
        
        .related-tools-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-xl);
            font-size: 1.5rem;
            font-weight: 700;
            text-align: center;
        }
        
        .related-tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
            justify-items: center;
        }
        
        .related-tool-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            margin-top: var(--spacing-sm);
            line-height: 1.3;
        }
        
        .related-tool-item:hover .related-tool-name {
            color: var(--primary-color);
        }
        
        /* === END: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        /* === START: STANDARDIZED FEATURES SECTION === */
        .features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }
        
        .features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
        }
        
        .features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            margin-bottom: 0.3em;
        }
        
        .features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 600px) {
            .features-list {
                columns: 1 !important;
                -webkit-columns: 1 !important;
                -moz-columns: 1 !important;
            }
        }
        /* === END: STANDARDIZED FEATURES SECTION === */

        /* Responsive Design */
        @media (max-width: 768px) {
            .text-sorter-widget-container {
                margin: var(--spacing-md);
                padding: var(--spacing-lg);
            }
            .text-sorter-widget-title { font-size: 1.875rem; }
            .text-sorter-buttons { flex-direction: column; }
            .text-sorter-btn { flex: none; }
            .text-sorter-options { grid-template-columns: 1fr; }
            
            .related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .related-tool-item { padding: var(--spacing-md); max-width: none; }
            .related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .related-tool-name { font-size: 0.75rem; }
        }
        
        /* Dark Mode Support */
        [data-theme="dark"] .text-sorter-textarea:focus {
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }

        /* Focus & Accessibility */
        .text-sorter-radio:focus,
        .text-sorter-checkbox:focus,
        .text-sorter-btn:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .text-sorter-output::selection {
            background-color: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="text-sorter-widget-container">
        <h1 class="text-sorter-widget-title">Text Sorter</h1>
        <p class="text-sorter-widget-description">
            Sort lines of text alphabetically, numerically, by length, or in reverse order. Perfect for organizing lists, data, and content.
        </p>
        
        <div class="text-sorter-input-group">
            <label for="textSorterInput" class="text-sorter-label">Enter text to sort (one item per line):</label>
            <textarea 
                id="textSorterInput" 
                class="text-sorter-textarea"
                placeholder="Enter each item on a new line..."
                rows="6"
            ></textarea>
        </div>

        <div class="text-sorter-options">
            <div class="text-sorter-option">
                <input type="radio" id="sortAlphabetical" name="sortType" class="text-sorter-radio" value="alphabetical" checked>
                <label for="sortAlphabetical" class="text-sorter-option-label">Alphabetical (A-Z)</label>
            </div>
            <div class="text-sorter-option">
                <input type="radio" id="sortNumerical" name="sortType" class="text-sorter-radio" value="numerical">
                <label for="sortNumerical" class="text-sorter-option-label">Numerical</label>
            </div>
            <div class="text-sorter-option">
                <input type="radio" id="sortLength" name="sortType" class="text-sorter-radio" value="length">
                <label for="sortLength" class="text-sorter-option-label">By Length</label>
            </div>
            <div class="text-sorter-option">
                <input type="checkbox" id="sortReverse" class="text-sorter-checkbox">
                <label for="sortReverse" class="text-sorter-option-label">Reverse Order</label>
            </div>
            <div class="text-sorter-option">
                <input type="checkbox" id="sortCaseSensitive" class="text-sorter-checkbox">
                <label for="sortCaseSensitive" class="text-sorter-option-label">Case Sensitive</label>
            </div>
            <div class="text-sorter-option">
                <input type="checkbox" id="sortRemoveDuplicates" class="text-sorter-checkbox">
                <label for="sortRemoveDuplicates" class="text-sorter-option-label">Remove Duplicates</label>
            </div>
        </div>

        <div class="text-sorter-buttons">
            <button class="text-sorter-btn text-sorter-btn-primary" onclick="TextSorter.sort()">
                Sort Text
            </button>
            <button class="text-sorter-btn text-sorter-btn-secondary" onclick="TextSorter.clear()">
                Clear All
            </button>
            <button class="text-sorter-btn text-sorter-btn-success" onclick="TextSorter.copy()">
                Copy Result
            </button>
        </div>

        <div class="text-sorter-result">
            <h3 class="text-sorter-result-title">Sorted Text:</h3>
            <div class="text-sorter-output" id="textSorterOutput">
                Your sorted text will appear here...
            </div>
        </div>

        <div class="related-tools">
            <h3 class="related-tools-title">Related Tools</h3>
            <div class="related-tools-grid">
                <a href="/p/text-to-slug_30.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon"><i class="fas fa-link"></i></div>
                    <div class="related-tool-name">Text to Slug</div>
                </a>
                <a href="/p/text-repeater.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon"><i class="fas fa-redo"></i></div>
                    <div class="related-tool-name">Text Repeater</div>
                </a>
                <a href="/p/comma-separator.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon"><i class="fas fa-ellipsis-h"></i></div>
                    <div class="related-tool-name">Comma Separator</div>
                </a>
            </div>
        </div>

        <div class="features">
            <h3 class="features-title">Key Features:</h3>
            <ul class="features-list">
                <li class="features-item">Sort alphabetically (A-Z) or reverse</li>
                <li class="features-item">Sort numerically (ascending or descending)</li>
                <li class="features-item">Sort by line length</li>
                <li class="features-item">Case-sensitive sorting option</li>
                <li class="features-item">Remove duplicate lines easily</li>
                <li class="features-item">One-click copy and clear</li>
            </ul>
        </div>
        
    </div>

    <!-- Copy notification -->
    <div class="text-sorter-notification" id="textSorterNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Text Sorter Tool
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('textSorterInput'),
                output: () => document.getElementById('textSorterOutput'),
                notification: () => document.getElementById('textSorterNotification')
            };

            window.TextSorter = {
                sort() {
                    const input = elements.input();
                    const output = elements.output();
                    const text = input.value;

                    if (!text.trim()) {
                        output.textContent = 'Please enter some text to sort.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    let lines = text.split('\n');

                    // Filter out empty lines *after* preserving original structure if needed
                    const originalLength = lines.length;
                    lines = lines.filter(line => line.trim() !== '' || line === ''); 
                    if (lines.every(line => line === '')) {
                         lines = lines.filter(line => line !== '');
                    }
                    if (lines.length === 0 && text.trim() !== '') {
                        lines = text.split('\n').filter(line => line.trim() !== '');
                    }


                    if (lines.length === 0) {
                        output.textContent = 'No valid lines found to sort.';
                        return;
                    }

                    const options = this.getOptions();
                    const sortedLines = this.processLines(lines, options);
                    output.textContent = sortedLines.join('\n');
                },

                getOptions() {
                    const sortType = document.querySelector('input[name="sortType"]:checked').value;
                    return {
                        type: sortType,
                        reverse: document.getElementById('sortReverse').checked,
                        caseSensitive: document.getElementById('sortCaseSensitive').checked,
                        removeDuplicates: document.getElementById('sortRemoveDuplicates').checked
                    };
                },

                processLines(lines, options) {
                    let processedLines = [...lines];

                    // Handle duplicates before filtering empty lines if needed
                    if (options.removeDuplicates) {
                        const seen = new Set();
                        processedLines = processedLines.filter(line => {
                            const key = options.caseSensitive ? line : line.toLowerCase();
                            if (seen.has(key)) {
                                return false;
                            }
                            seen.add(key);
                            return true;
                        });
                    }
                    
                    // Filter empty lines here to not affect sorting logic
                    processedLines = processedLines.filter(line => line.trim() !== '');

                    // Sort based on type
                    switch (options.type) {
                        case 'alphabetical':
                            processedLines.sort((a, b) => {
                                const strA = options.caseSensitive ? a : a.toLowerCase();
                                const strB = options.caseSensitive ? b : b.toLowerCase();
                                return strA.localeCompare(strB, undefined, { numeric: true });
                            });
                            break;
                        case 'numerical':
                            processedLines.sort((a, b) => {
                                const numA = parseFloat(a.trim().match(/^-?\d*\.?\d+/)?.[0] || NaN);
                                const numB = parseFloat(b.trim().match(/^-?\d*\.?\d+/)?.[0] || NaN);
                                if (isNaN(numA) && isNaN(numB)) return 0;
                                if (isNaN(numA)) return 1;
                                if (isNaN(numB)) return -1;
                                return numA - numB;
                            });
                            break;
                        case 'length':
                            processedLines.sort((a, b) => a.length - b.length);
                            break;
                    }

                    // Reverse if requested
                    if (options.reverse) {
                        processedLines.reverse();
                    }

                    return processedLines;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your sorted text will appear here...';
                    elements.output().style.color = '';
                    document.getElementById('sortAlphabetical').checked = true;
                    document.getElementById('sortReverse').checked = false;
                    document.getElementById('sortCaseSensitive').checked = false;
                    document.getElementById('sortRemoveDuplicates').checked = false;
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your sorted text will appear here...', 'Please enter some text to sort.', 'No valid lines found to sort.'].includes(text)) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                const sortOptions = document.querySelectorAll('input[name="sortType"], #sortReverse, #sortCaseSensitive, #sortRemoveDuplicates');

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Auto-sort on input changes
                input.addEventListener('input', function() {
                    if (this.value.trim() || this.value.includes('\n')) {
                        TextSorter.sort();
                    } else {
                        elements.output().textContent = 'Your sorted text will appear here...';
                        elements.output().style.color = '';
                    }
                });

                // Auto-sort when options change
                sortOptions.forEach(option => {
                    option.addEventListener('change', () => {
                        if (input.value.trim() || input.value.includes('\n')) TextSorter.sort();
                    });
                });

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        TextSorter.sort();
                    }
                });
            });
        })();
    </script>
</body>
</html>