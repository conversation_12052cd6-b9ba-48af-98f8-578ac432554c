<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Case Converter Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Case Converter - Transform Text Case Instantly",
        "description": "Convert text between different cases instantly. Free online tool supporting uppercase, lowercase, title case, camel case, snake case, and more with real-time conversion.",
        "url": "https://www.webtoolskit.org/p/case-converter.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Case Converter",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Text Case" },
            { "@type": "CopyAction", "name": "Copy Converted Text" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do you convert caps to normal text?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To convert all caps text to normal text (like lowercase or sentence case), you can use an online case converter. Simply paste your uppercase text into the input box and click the 'lowercase' or 'Sentence case' button. The tool will instantly change the text to the desired format, which you can then copy."
          }
        },
        {
          "@type": "Question",
          "name": "How do I change caps to lowercase without retyping?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Using a case converter tool is the fastest way to change text from all caps to lowercase without retyping. Just copy the text you want to convert, paste it into the tool, click the 'lowercase' button, and then copy the converted result. The entire process takes only a few seconds."
          }
        },
        {
          "@type": "Question",
          "name": "How to change capital letters to small letters using a keyboard shortcut?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Many applications have their own keyboard shortcuts. For example, in Microsoft Word, you can select text and press Shift+F3 to cycle through uppercase, lowercase, and title case. In Google Docs, use the menu: Format > Text > Capitalization > lowercase. However, there is no universal keyboard shortcut that works everywhere, which is why a browser-based case converter is so useful for quick conversions across any application."
          }
        },
        {
          "@type": "Question",
          "name": "How do I uncapitalize text in Google Docs?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To uncapitalize (convert to lowercase) text in Google Docs, select the text you want to change. Then, go to the menu bar at the top, click 'Format' -> 'Text' -> 'Capitalization' -> 'lowercase'."
          }
        },
        {
          "@type": "Question",
          "name": "What is sentence casing?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Sentence casing is a capitalization style where the first letter of the first word in a sentence is capitalized, along with any proper nouns. All other letters are lowercase. It is the standard format for writing sentences in English and many other languages. For example: 'The quick brown fox jumps over the lazy dog.'"
          }
        }
      ]
    }
    </script>


    <style>
        /* Mobile-First Reset */
        * {
            box-sizing: border-box;
        }

        html {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Case Converter - Enhanced with Text to Slug features */
        .widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
            width: 100%;
            box-sizing: border-box;
        }

        .widget-container * { box-sizing: border-box; }

        .widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .case-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .case-btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            min-height: 44px; /* Minimum touch target size */
            touch-action: manipulation; /* Prevents double-tap zoom */
        }

        .case-btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
            min-height: 44px; /* Minimum touch target size */
            touch-action: manipulation; /* Prevents double-tap zoom */
        }

        .btn:hover { transform: translateY(-2px); }

        .btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover { background-color: var(--border-color); }

        .btn-success {
            background-color: #10b981;
            color: white;
        }

        .btn-success:hover { background-color: #059669; }

        .result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: var(--font-size-base);
            min-height: 120px;
            color: var(--text-color);
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .notification.show { transform: translateX(0); }

        /* === START: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */

        /* Enhanced look for a more realistic, professional icon */
        .related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm); /* Tighter margin */
            border-radius: 18px; /* Modern "squircle" shape */
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            /* Subtle, realistic shadow for a 3D effect */
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        /* Specific, natural colors for each tool icon based on data source */
        a[href*="text-to-slug"] .related-tool-icon {
            background: linear-gradient(145deg, #2563eb, #1d4ed8); /* Professional Blue */
        }
        a[href*="word-counter"] .related-tool-icon {
            background: linear-gradient(145deg, #10B981, #059669); /* Analytical Green */
        }
        a[href*="remove-line-breaks"] .related-tool-icon {
            background: linear-gradient(145deg, #F59E0B, #D97706); /* Functional Amber */
        }

        /* Enhanced hover effect */
        .related-tool-item:hover .related-tool-icon {
            transform: translateY(-5px) scale(1.05); /* Smoother lift effect */
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }

        /* Specific hover colors for each tool icon */
        a[href*="text-to-slug"]:hover .related-tool-icon {
            background: linear-gradient(145deg, #3b82f6, #2563eb);
        }
        a[href*="word-counter"]:hover .related-tool-icon {
            background: linear-gradient(145deg, #14b8a6, #10B981);
        }
        a[href*="remove-line-breaks"]:hover .related-tool-icon {
            background: linear-gradient(145deg, #f97316, #F59E0B);
        }

        .related-tool-item {
            box-shadow: none;
            border: none;
            text-align: center;
            text-decoration: none;
            color: inherit;
            transition: var(--transition-base);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            display: block;
            width: 100%;
            max-width: 160px;
        }

        .related-tool-item:hover {
            transform: translateY(0);
            background-color: transparent;
            box-shadow: none;
            border: none;
        }
        
        /* Related Tools Section */
        .related-tools {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .related-tools-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-xl);
            font-size: 1.5rem;
            font-weight: 700;
            text-align: center;
        }

        /* Tighter grid for a more compact layout */
        .related-tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--spacing-lg); /* Reduced spacing */
            margin-top: var(--spacing-lg);
            justify-items: center;
        }

        .related-tool-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            margin-top: var(--spacing-sm);
            line-height: 1.3;
        }

        .related-tool-item:hover .related-tool-name {
            color: var(--primary-color);
        }
        
        /* === END: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */
        
        /* SEO Content Section Styles */
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content a { color: var(--primary-color); text-decoration: none; }
        .seo-content a:hover { text-decoration: underline; }


        /* Key Features Section - Enhanced */
        .features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            margin-bottom: 0.3em;
        }

        .features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        /* Mobile-First Responsive Design */
        @media (max-width: 768px) {
            .widget-container {
                margin: 10px;
                padding: 20px;
            }

            .widget-title {
                font-size: 1.75rem;
            }

            .widget-description {
                font-size: 1rem;
                margin-bottom: 20px;
            }

            .case-buttons {
                grid-template-columns: 1fr 1fr;
                gap: 12px;
            }

            .case-btn {
                padding: 12px 8px;
                font-size: 0.9rem;
            }

            .buttons {
                flex-direction: column;
                gap: 12px;
            }

            .btn {
                flex: none;
                min-width: auto;
                width: 100%;
            }

            .textarea {
                padding: 15px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .output {
                padding: 15px;
                font-size: 14px;
            }
            
            /* Responsive adjustments for related tools */
            .related-tools-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: var(--spacing-md); /* Further reduced spacing */
            }

            .related-tool-item {
                padding: var(--spacing-md);
                max-width: none;
            }

            .related-tool-icon {
                width: 64px;
                height: 64px;
                font-size: 2rem;
                border-radius: 16px;
            }

            .related-tool-name {
                font-size: 0.875rem;
            }


            .features-list {
                columns: 1;
                -webkit-columns: 1;
                -moz-columns: 1;
            }
        }

        @media (max-width: 480px) {
            .widget-container {
                margin: 5px;
                padding: 15px;
            }

            .widget-title {
                font-size: 1.5rem;
            }

            .case-buttons {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .case-btn {
                padding: 14px 12px;
                font-size: 0.9rem;
            }

            .related-tools-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: var(--spacing-sm); /* Minimized spacing */
            }

            .related-tool-item {
                padding: var(--spacing-sm);
                max-width: none;
            }

            .related-tool-icon {
                width: 56px;
                height: 56px;
                font-size: 1.75rem;
                border-radius: 12px;
            }

            .related-tool-name {
                font-size: 0.75rem;
            }

            .notification {
                top: 10px;
                right: 10px;
                left: 10px;
                transform: translateY(-100px);
                text-align: center;
            }

            .notification.show {
                transform: translateY(0);
            }
        }

        /* Dark Mode Support */
        [data-theme="dark"] .textarea:focus {
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }

        /* Focus & Accessibility */
        .case-btn:focus,
        .btn:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .output::selection {
            background-color: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="widget-container">
        <h1 class="widget-title">Case Converter</h1>
        <p class="widget-description">
            Transform text between different cases instantly. Convert to uppercase, lowercase, title case, camel case, and more.
        </p>

        <label for="input" class="label">Enter your text:</label>
        <textarea
            id="input"
            class="textarea"
            placeholder="Type or paste your text here to convert between different cases..."
            rows="4"
        ></textarea>

        <div class="case-buttons">
            <button class="case-btn" onclick="Tool.convert('uppercase')">UPPERCASE</button>
            <button class="case-btn" onclick="Tool.convert('lowercase')">lowercase</button>
            <button class="case-btn" onclick="Tool.convert('titlecase')">Title Case</button>
            <button class="case-btn" onclick="Tool.convert('sentencecase')">Sentence case</button>
            <button class="case-btn" onclick="Tool.convert('camelcase')">camelCase</button>
            <button class="case-btn" onclick="Tool.convert('pascalcase')">PascalCase</button>
            <button class="case-btn" onclick="Tool.convert('snakecase')">snake_case</button>
            <button class="case-btn" onclick="Tool.convert('kebabcase')">kebab-case</button>
        </div>

        <div class="buttons">
            <button class="btn btn-secondary" onclick="Tool.clear()">Clear All</button>
            <button class="btn btn-success" onclick="Tool.copy()">Copy Result</button>
        </div>

        <div class="result">
            <h3 class="result-title">Converted Text:</h3>
            <div class="output" id="output">Your converted text will appear here...</div>
        </div>

        <div class="related-tools">
            <h3 class="related-tools-title">Related Tools</h3>
            <div class="related-tools-grid">
                <a href="/p/text-to-slug_30.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="related-tool-name">Text to Slug</div>
                </a>

                <a href="/p/word-counter.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="related-tool-name">Word Counter</div>
                </a>

                <a href="/p/remove-line-breaks.html" class="related-tool-item" rel="noopener">
                    <div class="related-tool-icon">
                        <i class="fas fa-align-left"></i>
                    </div>
                    <div class="related-tool-name">Remove Line Breaks</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Effortless Text Transformation with Our Case Converter</h2>
            <p>A <strong>Case Converter</strong> is a versatile online utility designed to instantly change the capitalization of your text without forcing you to retype anything. Whether you're a writer standardizing headlines, a developer formatting variable names, or a student fixing a paper, this tool saves you time and ensures consistency. You can effortlessly switch between cases like UPPERCASE, lowercase, Title Case, Sentence case, and even programming-specific formats like camelCase and snake_case.</p>
            
            <h3>How to Change Text Case Instantly</h3>
            <p>Our tool simplifies the conversion process into three easy steps:</p>
            <ol>
              <li><strong>Input Your Text:</strong> Copy and paste your text into the input field above.</li>
              <li><strong>Select a Case:</strong> Click on any of the conversion buttons (e.g., 'UPPERCASE', 'Title Case') to instantly transform your text. The result will appear in the output box below.</li>
              <li><strong>Copy the Result:</strong> Use the 'Copy Result' button to copy the newly formatted text to your clipboard, ready to be pasted wherever you need it.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Case Converter</h3>
            
            <h4>How do you convert caps to normal text?</h4>
            <p>To convert text from ALL CAPS to a more standard format, paste your text into the converter. For regular sentences, click the 'Sentence case' button. This capitalizes the first letter of each sentence and makes the rest lowercase. If you want everything to be small letters, simply click the 'lowercase' button.</p>
            
            <h4>How do I change caps to lowercase without retyping?</h4>
            <p>This case converter is the perfect solution. Instead of manually editing each letter, just copy your uppercase text, paste it into the tool, and click the 'lowercase' button. The entire block of text is converted in a fraction of a second, which you can then copy and use immediately.</p>
            
            <h4>How to change capital letters to small letters using a keyboard shortcut?</h4>
            <p>While some applications like Microsoft Word (Shift+F3) have built-in shortcuts, there isn't a universal one that works across all programs and web browsers. Our online tool provides a reliable, click-based alternative that works on any device without needing to remember different shortcuts for different apps.</p>
            
            <h4>How do I uncapitalize text in Google Docs?</h4>
            <p>In Google Docs, you can easily uncapitalize text by highlighting it, then navigating to the menu and selecting <strong>Format → Text → Capitalization → lowercase</strong>. For quick conversions or when working outside of Google Docs, our tool remains a faster option.</p>
            
            <h4>What is sentence casing?</h4>
            <p>Sentence case is the standard capitalization style used in writing, where only the first letter of the first word in a sentence is capitalized, along with any proper nouns. Our 'Sentence case' button automatically applies this rule, making it perfect for correcting text to be grammatically standard.</p>
        </div>

        <div class="features">
            <h3 class="features-title">Key Features:</h3>
            <ul class="features-list">
                <li class="features-item">Instant case conversion</li>
                <li class="features-item">Multiple case types supported</li>
                <li class="features-item">Real-time preview</li>
                <li class="features-item">One-click copy to clipboard</li>
                <li class="features-item">Keyboard shortcuts support</li>
                <li class="features-item">Mobile-responsive design</li>
                <li class="features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="notification" id="notification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Enhanced Case Converter with features from Text to Slug
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('input'),
                output: () => document.getElementById('output'),
                notification: () => document.getElementById('notification')
            };

            const cases = {
                uppercase: text => text.toUpperCase(),
                lowercase: text => text.toLowerCase(),
                titlecase: text => text.toLowerCase().replace(/\b\w/g, c => c.toUpperCase()),
                sentencecase: text => text.toLowerCase().replace(/(^\w|\.\s+\w)/g, c => c.toUpperCase()),
                camelcase: text => text.toLowerCase().replace(/[^a-zA-Z0-9]+(.)/g, (m, c) => c.toUpperCase()).replace(/^[A-Z]/, c => c.toLowerCase()),
                pascalcase: text => text.toLowerCase().replace(/[^a-zA-Z0-9]+(.)/g, (m, c) => c.toUpperCase()).replace(/^[a-z]/, c => c.toUpperCase()),
                snakecase: text => text.toLowerCase().replace(/[^a-zA-Z0-9]+/g, '_').replace(/^_+|_+$/g, ''),
                kebabcase: text => text.toLowerCase().replace(/[^a-zA-Z0-9]+/g, '-').replace(/^-+|-+$/g, '')
            };

            window.Tool = {
                convert(type) {
                    const input = elements.input();
                    const output = elements.output();
                    const text = input.value;

                    if (!text.trim()) {
                        output.textContent = 'Please enter some text to convert.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    output.textContent = cases[type] ? cases[type](text) : text;
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your converted text will appear here...';
                    elements.output().style.color = '';
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your converted text will appear here...', 'Please enter some text to convert.'].includes(text)) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            // Initialize with enhanced features
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Handle Ctrl+Enter for quick conversion (defaults to lowercase)
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        Tool.convert('lowercase');
                    }
                });

                // Auto-clear output when input is empty
                input.addEventListener('input', function() {
                    if (!this.value.trim()) {
                        elements.output().textContent = 'Your converted text will appear here...';
                        elements.output().style.color = '';
                    }
                });
            });
        })();
    </script>
</body>
</html>