<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Resizer Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Image Resizer - Resize Images Online Without Quality Loss",
        "description": "Resize images online for free with custom dimensions and preset sizes. Professional image resizing tool for web, social media, and print with quality preservation.",
        "url": "https://www.webtoolskit.org/p/image-resizer_30.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Image Resizer",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "CreateAction", "name": "Resize Image" },
            { "@type": "DownloadAction", "name": "Download Resized Image" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I resize an image without losing quality?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To resize an image without losing quality, use a professional image resizer that employs high-quality interpolation algorithms. Our free tool uses advanced scaling techniques that preserve image sharpness and detail when reducing or increasing image dimensions, ensuring your resized images maintain their visual quality."
          }
        },
        {
          "@type": "Question",
          "name": "What is the best free image resizer online?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The best free image resizer online should offer custom dimensions, preset sizes, quality preservation, and multiple format support. Our tool provides all these features plus aspect ratio maintenance, social media presets, and works entirely in your browser for privacy and speed without requiring registration."
          }
        },
        {
          "@type": "Question",
          "name": "How do I resize an image to specific dimensions?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To resize an image to specific dimensions, upload your image, enter your desired width and height in pixels in the custom dimensions fields, choose whether to maintain aspect ratio, then click 'Resize Image'. The tool will process your image to the exact dimensions you specified while preserving quality."
          }
        },
        {
          "@type": "Question",
          "name": "Can I resize multiple images at once?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our current tool processes one image at a time to ensure optimal quality and browser performance. However, you can quickly resize multiple images by processing them individually. Each resized image can be downloaded immediately, making it efficient for handling several images in succession."
          }
        },
        {
          "@type": "Question",
          "name": "How to resize an image for web or social media?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To resize images for web or social media, use our preset options like 'Instagram Square (1080x1080)', 'Facebook Cover (820x312)', or 'Web Thumbnail (300x300)'. These presets are optimized for each platform's requirements and ensure your images look perfect when posted online."
          }
        }
      ]
    }
    </script>

    <style>
        /* Image Resizer Widget - Simplified & Template Compatible */
        .image-resizer-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .image-resizer-widget-container * { box-sizing: border-box; }

        .image-resizer-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .image-resizer-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .image-resizer-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .image-resizer-upload-area {
            width: 100%;
            padding: var(--spacing-xl);
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            background-color: var(--background-color-alt);
            color: var(--text-color);
            text-align: center;
            cursor: pointer;
            transition: var(--transition-base);
            margin-bottom: var(--spacing-lg);
            position: relative;
        }

        .image-resizer-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .image-resizer-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
            transform: scale(1.02);
        }

        .image-resizer-upload-text {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .image-resizer-upload-subtext {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .image-resizer-file-input {
            display: none;
        }

        .image-resizer-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .image-resizer-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .image-resizer-checkbox {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .image-resizer-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
        }

        .image-resizer-custom-dimensions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .image-resizer-dimension-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .image-resizer-dimension-input {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            background-color: var(--card-bg);
            font-family: var(--font-family);
            color: var(--text-color);
        }

        .image-resizer-dimension-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .image-resizer-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .image-resizer-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .image-resizer-btn:hover { transform: translateY(-2px); }

        .image-resizer-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .image-resizer-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .image-resizer-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .image-resizer-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .image-resizer-btn-success {
            background-color: #10b981;
            color: white;
        }

        .image-resizer-btn-success:hover {
            background-color: #059669;
        }

        .image-resizer-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-lg);
        }

        .image-resizer-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .image-resizer-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }

        .image-resizer-preview-section {
            text-align: center;
        }

        .image-resizer-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
            display: block;
        }

        .image-resizer-preview-image {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .image-resizer-preview-info {
            margin-top: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .image-resizer-processing {
            display: none;
            text-align: center;
            padding: var(--spacing-lg);
            color: var(--primary-color);
            font-weight: 600;
        }

        .image-resizer-processing::before {
            content: "📐 ";
            font-size: 1.2em;
        }

        .image-resizer-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="image-enlarger"] .image-resizer-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-cropper"] .image-resizer-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="image-converter"] .image-resizer-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .image-resizer-related-tool-item:hover .image-resizer-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="image-enlarger"]:hover .image-resizer-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-cropper"]:hover .image-resizer-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="image-converter"]:hover .image-resizer-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .image-resizer-related-tool-item { box-shadow: none; border: none; }
        .image-resizer-related-tool-item:hover { box-shadow: none; border: none; }
        .image-resizer-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .image-resizer-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .image-resizer-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .image-resizer-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .image-resizer-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .image-resizer-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .image-resizer-related-tool-item:hover .image-resizer-related-tool-name { color: var(--primary-color); }

        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .image-resizer-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .image-resizer-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .image-resizer-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .image-resizer-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .image-resizer-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }

        .image-resizer-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .image-resizer-notification.show { transform: translateX(0); }

        @media (max-width: 768px) {
            .image-resizer-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .image-resizer-widget-title { font-size: 1.875rem; }
            .image-resizer-buttons { flex-direction: column; }
            .image-resizer-btn { flex: none; }
            .image-resizer-options { grid-template-columns: 1fr; }
            .image-resizer-custom-dimensions { grid-template-columns: 1fr; }
            .image-resizer-preview { grid-template-columns: 1fr; }
            .image-resizer-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .image-resizer-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .image-resizer-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .image-resizer-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .image-resizer-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .image-resizer-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .image-resizer-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .image-resizer-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .image-resizer-upload-area:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .image-resizer-checkbox:focus, .image-resizer-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .image-resizer-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .image-resizer-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="image-resizer-widget-container">
        <h1 class="image-resizer-widget-title">Image Resizer</h1>
        <p class="image-resizer-widget-description">
            Resize images online with custom dimensions and preset sizes. Perfect for web, social media, and professional use with quality preservation.
        </p>
        
        <div class="image-resizer-input-group">
            <label for="imageResizerInput" class="image-resizer-label">Select your image:</label>
            <div class="image-resizer-upload-area" id="imageResizerUploadArea">
                <div class="image-resizer-upload-text">📏 Click to select or drag & drop your image</div>
                <div class="image-resizer-upload-subtext">Supports JPG, PNG, WebP, GIF (max 10MB)</div>
                <input type="file" id="imageResizerInput" class="image-resizer-file-input" accept="image/*">
            </div>
        </div>

        <div class="image-resizer-options">
            <div class="image-resizer-option">
                <input type="checkbox" id="resizerInstagram" class="image-resizer-checkbox" checked>
                <label for="resizerInstagram" class="image-resizer-option-label">Instagram (1080x1080)</label>
            </div>
            <div class="image-resizer-option">
                <input type="checkbox" id="resizerFacebook" class="image-resizer-checkbox">
                <label for="resizerFacebook" class="image-resizer-option-label">Facebook Cover (820x312)</label>
            </div>
            <div class="image-resizer-option">
                <input type="checkbox" id="resizerThumbnail" class="image-resizer-checkbox">
                <label for="resizerThumbnail" class="image-resizer-option-label">Web Thumbnail (300x300)</label>
            </div>
            <div class="image-resizer-option">
                <input type="checkbox" id="resizerCustom" class="image-resizer-checkbox">
                <label for="resizerCustom" class="image-resizer-option-label">Custom Dimensions</label>
            </div>
        </div>

        <div class="image-resizer-custom-dimensions" id="imageResizerCustomDimensions" style="display: none;">
            <div class="image-resizer-dimension-group">
                <label class="image-resizer-label">Width (px):</label>
                <input type="number" id="imageResizerWidth" class="image-resizer-dimension-input" placeholder="e.g., 800" min="1" max="5000">
            </div>
            <div class="image-resizer-dimension-group">
                <label class="image-resizer-label">Height (px):</label>
                <input type="number" id="imageResizerHeight" class="image-resizer-dimension-input" placeholder="e.g., 600" min="1" max="5000">
            </div>
        </div>

        <div class="image-resizer-buttons">
            <button class="image-resizer-btn image-resizer-btn-primary" onclick="ImageResizer.resize()">
                Resize Image
            </button>
            <button class="image-resizer-btn image-resizer-btn-secondary" onclick="ImageResizer.clear()">
                Clear All
            </button>
            <button class="image-resizer-btn image-resizer-btn-success" onclick="ImageResizer.download()">
                Download Result
            </button>
        </div>

        <div class="image-resizer-processing" id="imageResizerProcessing">
            Processing your image...
        </div>

        <div class="image-resizer-result" id="imageResizerResult" style="display: none;">
            <h3 class="image-resizer-result-title">Image Preview:</h3>
            <div class="image-resizer-preview">
                <div class="image-resizer-preview-section">
                    <span class="image-resizer-preview-label">Original</span>
                    <img id="imageResizerOriginal" class="image-resizer-preview-image" alt="Original image">
                    <div class="image-resizer-preview-info" id="imageResizerOriginalInfo"></div>
                </div>
                <div class="image-resizer-preview-section">
                    <span class="image-resizer-preview-label">Resized</span>
                    <img id="imageResizerResized" class="image-resizer-preview-image" alt="Resized image">
                    <div class="image-resizer-preview-info" id="imageResizerResizedInfo"></div>
                </div>
            </div>
        </div>

        <div class="image-resizer-related-tools">
            <h3 class="image-resizer-related-tools-title">Related Tools</h3>
            <div class="image-resizer-related-tools-grid">
                <a href="/p/image-enlarger.html" class="image-resizer-related-tool-item" rel="noopener">
                    <div class="image-resizer-related-tool-icon">
                        <i class="fas fa-search-plus"></i>
                    </div>
                    <div class="image-resizer-related-tool-name">Image Enlarger</div>
                </a>

                <a href="/p/image-cropper.html" class="image-resizer-related-tool-item" rel="noopener">
                    <div class="image-resizer-related-tool-icon">
                        <i class="fas fa-crop"></i>
                    </div>
                    <div class="image-resizer-related-tool-name">Image Cropper</div>
                </a>

                <a href="/p/image-converter.html" class="image-resizer-related-tool-item" rel="noopener">
                    <div class="image-resizer-related-tool-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="image-resizer-related-tool-name">Image Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Professional Image Resizing Tool for Perfect Dimensions</h2>
            <p>Need to resize images for web, social media, or print? Our free <strong>Image Resizer</strong> tool provides precise control over image dimensions while maintaining quality. Whether you're preparing images for Instagram, Facebook covers, web thumbnails, or custom requirements, this tool ensures your images look perfect at any size.</p>
            <p>Unlike basic image editors that can degrade quality during resizing, our tool uses advanced scaling algorithms to preserve image sharpness and detail. Choose from popular preset sizes or enter custom dimensions for complete control over your image output.</p>
            
            <h3>How to Resize Your Images</h3>
            <ol>
                <li><strong>Upload Your Image:</strong> Click to select or drag and drop your image file (supports JPG, PNG, WebP, GIF formats).</li>
                <li><strong>Choose Size:</strong> Select from preset sizes like Instagram Square, Facebook Cover, or choose Custom Dimensions for specific width and height.</li>
                <li><strong>Resize and Download:</strong> Click "Resize Image" to process, then download your perfectly sized result.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Image Resizing</h3>
            
            <h4>How do I resize an image without losing quality?</h4>
            <p>To resize an image without losing quality, use a professional image resizer that employs high-quality interpolation algorithms. Our free tool uses advanced scaling techniques that preserve image sharpness and detail when reducing or increasing image dimensions, ensuring your resized images maintain their visual quality.</p>
            
            <h4>What is the best free image resizer online?</h4>
            <p>The best free image resizer online should offer custom dimensions, preset sizes, quality preservation, and multiple format support. Our tool provides all these features plus aspect ratio maintenance, social media presets, and works entirely in your browser for privacy and speed without requiring registration.</p>
            
            <h4>How do I resize an image to specific dimensions?</h4>
            <p>To resize an image to specific dimensions, upload your image, enter your desired width and height in pixels in the custom dimensions fields, choose whether to maintain aspect ratio, then click 'Resize Image'. The tool will process your image to the exact dimensions you specified while preserving quality.</p>
            
            <h4>Can I resize multiple images at once?</h4>
            <p>Our current tool processes one image at a time to ensure optimal quality and browser performance. However, you can quickly resize multiple images by processing them individually. Each resized image can be downloaded immediately, making it efficient for handling several images in succession.</p>
            
            <h4>How to resize an image for web or social media?</h4>
            <p>To resize images for web or social media, use our preset options like 'Instagram Square (1080x1080)', 'Facebook Cover (820x312)', or 'Web Thumbnail (300x300)'. These presets are optimized for each platform's requirements and ensure your images look perfect when posted online.</p>
        </div>

        <div class="image-resizer-features">
            <h3 class="image-resizer-features-title">Key Features:</h3>
            <ul class="image-resizer-features-list">
                <li class="image-resizer-features-item" style="margin-bottom: 0.3em;">Custom dimension control</li>
                <li class="image-resizer-features-item" style="margin-bottom: 0.3em;">Social media presets</li>
                <li class="image-resizer-features-item" style="margin-bottom: 0.3em;">Quality preservation algorithms</li>
                <li class="image-resizer-features-item" style="margin-bottom: 0.3em;">Multiple format support</li>
                <li class="image-resizer-features-item" style="margin-bottom: 0.3em;">Instant preview comparison</li>
                <li class="image-resizer-features-item" style="margin-bottom: 0.3em;">Mobile-responsive design</li>
                <li class="image-resizer-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Notification -->
    <div class="image-resizer-notification" id="imageResizerNotification">
        ✓ Image resized successfully!
    </div>

    <script>
        // Simplified Image Resizer
        (function() {
            'use strict';

            let originalImage = null;
            let resizedCanvas = null;

            const elements = {
                uploadArea: () => document.getElementById('imageResizerUploadArea'),
                input: () => document.getElementById('imageResizerInput'),
                customDimensions: () => document.getElementById('imageResizerCustomDimensions'),
                widthInput: () => document.getElementById('imageResizerWidth'),
                heightInput: () => document.getElementById('imageResizerHeight'),
                result: () => document.getElementById('imageResizerResult'),
                processing: () => document.getElementById('imageResizerProcessing'),
                originalImg: () => document.getElementById('imageResizerOriginal'),
                resizedImg: () => document.getElementById('imageResizerResized'),
                originalInfo: () => document.getElementById('imageResizerOriginalInfo'),
                resizedInfo: () => document.getElementById('imageResizerResizedInfo'),
                notification: () => document.getElementById('imageResizerNotification')
            };

            window.ImageResizer = {
                resize() {
                    if (!originalImage) {
                        alert('Please select an image first.');
                        return;
                    }

                    const dimensions = this.getTargetDimensions();
                    if (!dimensions) {
                        alert('Please select a size preset or enter custom dimensions.');
                        return;
                    }

                    elements.processing().style.display = 'block';
                    elements.result().style.display = 'none';

                    setTimeout(() => {
                        try {
                            resizedCanvas = this.createResizedImage(originalImage, dimensions.width, dimensions.height);
                            this.displayResult(dimensions);
                            this.showNotification();
                        } catch (error) {
                            console.error('Error resizing image:', error);
                            alert('Error processing image. Please try again.');
                        }
                        elements.processing().style.display = 'none';
                    }, 100);
                },

                getTargetDimensions() {
                    if (document.getElementById('resizerInstagram').checked) {
                        return { width: 1080, height: 1080, name: 'Instagram Square' };
                    }
                    if (document.getElementById('resizerFacebook').checked) {
                        return { width: 820, height: 312, name: 'Facebook Cover' };
                    }
                    if (document.getElementById('resizerThumbnail').checked) {
                        return { width: 300, height: 300, name: 'Web Thumbnail' };
                    }
                    if (document.getElementById('resizerCustom').checked) {
                        const width = parseInt(elements.widthInput().value);
                        const height = parseInt(elements.heightInput().value);
                        if (width > 0 && height > 0) {
                            return { width, height, name: 'Custom Size' };
                        }
                    }
                    return null;
                },

                createResizedImage(img, targetWidth, targetHeight) {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    canvas.width = targetWidth;
                    canvas.height = targetHeight;

                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

                    return canvas;
                },

                displayResult(dimensions) {
                    const originalImg = elements.originalImg();
                    const resizedImg = elements.resizedImg();
                    const originalInfo = elements.originalInfo();
                    const resizedInfo = elements.resizedInfo();
                    const result = elements.result();

                    originalImg.src = originalImage.src;
                    resizedImg.src = resizedCanvas.toDataURL('image/png');
                    
                    originalInfo.textContent = `${originalImage.width} × ${originalImage.height} px`;
                    resizedInfo.textContent = `${dimensions.width} × ${dimensions.height} px (${dimensions.name})`;
                    
                    result.style.display = 'block';
                },

                download() {
                    if (!resizedCanvas) {
                        alert('Please resize an image first.');
                        return;
                    }

                    const link = document.createElement('a');
                    link.download = `resized-image-${Date.now()}.png`;
                    link.href = resizedCanvas.toDataURL('image/png');
                    link.click();
                },

                clear() {
                    originalImage = null;
                    resizedCanvas = null;
                    elements.input().value = '';
                    elements.widthInput().value = '';
                    elements.heightInput().value = '';
                    elements.result().style.display = 'none';
                    elements.processing().style.display = 'none';
                    elements.customDimensions().style.display = 'none';
                    
                    document.getElementById('resizerInstagram').checked = true;
                    document.getElementById('resizerFacebook').checked = false;
                    document.getElementById('resizerThumbnail').checked = false;
                    document.getElementById('resizerCustom').checked = false;
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                }
            };

            document.addEventListener('DOMContentLoaded', function() {
                const uploadArea = elements.uploadArea();
                const input = elements.input();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                uploadArea.addEventListener('click', () => input.click());
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });
                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });
                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        processFile(files[0]);
                    }
                });

                input.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        processFile(e.target.files[0]);
                    }
                });

                const checkboxes = document.querySelectorAll('.image-resizer-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        if (this.checked) {
                            checkboxes.forEach(cb => {
                                if (cb !== this) cb.checked = false;
                            });
                            
                            // Show/hide custom dimensions
                            if (this.id === 'resizerCustom') {
                                elements.customDimensions().style.display = 'grid';
                            } else {
                                elements.customDimensions().style.display = 'none';
                            }
                        }
                    });
                });

                function processFile(file) {
                    if (!file.type.startsWith('image/')) {
                        alert('Please select a valid image file.');
                        return;
                    }
                    if (file.size > 10 * 1024 * 1024) {
                        alert('File size must be less than 10MB.');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = new Image();
                        img.onload = function() {
                            originalImage = img;
                            originalImage.src = e.target.result;
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });
        })();
    </script>
</body>
</html>