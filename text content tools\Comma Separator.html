<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comma Separator Widget</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Comma Separator - Add & Remove Commas from Lists",
        "description": "Add or remove commas from lists and convert between different list formats. Free online tool with multiple separator options and formatting choices.",
        "url": "https://www.webtoolskit.org/p/comma-separator.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-15",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Comma Separator",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Add Commas to List" },
            { "@type": "ConvertAction", "name": "Remove Commas from List" },
            { "@type": "CopyAction", "name": "Copy Formatted List" }
        ]
    }
    </script>

    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a comma separator used for?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A comma separator is primarily used to delimit items in a list, making the data structured and machine-readable. It is the standard for CSV (Comma-Separated Values) files, used for importing/exporting data between spreadsheets and databases. It is also used to format lists of keywords, tags, or other items for use in various applications."
          }
        },
        {
          "@type": "Question",
          "name": "How do I add commas between words or numbers?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The easiest way is with an online comma separator tool. Paste your list of words or numbers (typically one item per line) into the input box, select the 'Add Commas' option, and the tool will automatically convert your vertical list into a single, comma-separated line of text."
          }
        },
        {
          "@type": "Question",
          "name": "Can I automatically insert commas in a list?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, this tool automatically inserts commas into a list. It detects items separated by new lines and joins them together with a comma and a space. This saves you from having to manually type a comma after each item in your list."
          }
        },
        {
          "@type": "Question",
          "name": "How do I separate values using a comma?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To separate values using a comma, you can copy your values from a source like a spreadsheet column or a text file where each value is on a new line. Paste the entire list into our tool, and it will generate a comma-separated string that you can use anywhere."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between comma and semicolon separators?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The main difference is regional and contextual. Commas are the most common separator for lists and CSV files in North America and many other parts of the world. Semicolons are often used as a list separator in European countries where the comma serves as a decimal point (e.g., '€3,50'). Semicolons are also used to separate more complex list items that may themselves contain commas."
          }
        }
      ]
    }
    </script>


    <style>
        /* Mobile-First Reset */
        * {
            box-sizing: border-box;
        }

        html {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Comma Separator Widget - Simplified & Template Compatible */
        .comma-separator-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
            width: 100%;
            box-sizing: border-box;
        }

        .comma-separator-widget-container * { box-sizing: border-box; }

        .comma-separator-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .comma-separator-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .comma-separator-label {
            display: block;
            margin-bottom: var(--spacing-sm);
            font-weight: 600;
            color: var(--text-color);
        }

        .comma-separator-textarea {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            font-size: var(--font-size-base);
            transition: var(--transition-base);
            resize: vertical;
            min-height: 120px;
            background-color: var(--background-color-alt);
            font-family: var(--font-family);
            color: var(--text-color);
            margin-bottom: var(--spacing-lg);
        }

        .comma-separator-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
            background-color: var(--card-bg);
        }

        .comma-separator-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .comma-separator-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .comma-separator-radio {
            width: 20px;
            height: 20px;
            accent-color: var(--primary-color);
            cursor: pointer;
        }

        .comma-separator-option-label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            color: var(--text-color);
            font-size: 0.95rem;
        }

        .comma-separator-control-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .comma-separator-input {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            background-color: var(--card-bg);
            color: var(--text-color);
            transition: var(--transition-base);
        }

        .comma-separator-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 71, 171, 0.1);
        }

        .comma-separator-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .comma-separator-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
            min-height: 44px; /* Minimum touch target size */
            touch-action: manipulation; /* Prevents double-tap zoom */
        }

        .comma-separator-btn:hover { transform: translateY(-2px); }

        .comma-separator-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .comma-separator-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .comma-separator-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .comma-separator-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .comma-separator-btn-success {
            background-color: #10b981;
            color: white;
        }

        .comma-separator-btn-success:hover {
            background-color: #059669;
        }

        .comma-separator-result {
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
            border: 1px solid var(--border-color);
        }

        .comma-separator-result-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .comma-separator-output {
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md) var(--spacing-lg);
            font-family: 'SF Mono', Monaco, monospace;
            font-size: var(--font-size-base);
            word-break: break-word;
            min-height: 120px;
            color: var(--text-color);
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .comma-separator-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .comma-separator-notification.show { transform: translateX(0); }
        
        /* SEO Content Section Styles */
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        
        /* === START: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */

        .comma-separator-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }

        a[href*="text-to-slug"] .comma-separator-related-tool-icon { background: linear-gradient(145deg, #2563eb, #1d4ed8); }
        a[href*="case-converter"] .comma-separator-related-tool-icon { background: linear-gradient(145deg, #EC4899, #DB2777); }
        a[href*="word-counter"] .comma-separator-related-tool-icon { background: linear-gradient(145deg, #10B981, #059669); }

        .comma-separator-related-tool-item:hover .comma-separator-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="text-to-slug"]:hover .comma-separator-related-tool-icon { background: linear-gradient(145deg, #3b82f6, #2563eb); }
        a[href*="case-converter"]:hover .comma-separator-related-tool-icon { background: linear-gradient(145deg, #f472b6, #EC4899); }
        a[href*="word-counter"]:hover .comma-separator-related-tool-icon { background: linear-gradient(145deg, #14b8a6, #10B981); }

        .comma-separator-related-tool-item {
            box-shadow: none; border: none; text-align: center; text-decoration: none; color: inherit;
            transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg);
            display: block; width: 100%; max-width: 160px;
        }

        .comma-separator-related-tool-item:hover { transform: translateY(0); background-color: transparent; box-shadow: none; border: none; }
        .comma-separator-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .comma-separator-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .comma-separator-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .comma-separator-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .comma-separator-related-tool-item:hover .comma-separator-related-tool-name { color: var(--primary-color); }
        
        /* === END: VISUAL ENHANCEMENTS FOR RELATED TOOLS SECTION === */

        /* Key Features Section - Enhanced */
        .comma-separator-features { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .comma-separator-features-title { color: var(--text-color); margin-bottom: var(--spacing-md); font-size: 1.25rem; font-weight: 700; }
        .comma-separator-features-list { list-style: none; padding: 0; margin: 0; columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        .comma-separator-features-item { padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg); color: var(--text-color-light); position: relative; margin-bottom: 0.3em; }
        .comma-separator-features-item:before { content: ""; position: absolute; left: 0; top: calc(var(--spacing-sm) + 2px); width: 12px; height: 6px; border-left: 2px solid #10b981; border-bottom: 2px solid #10b981; transform: rotate(-45deg); }

        /* Mobile-First Responsive Design */
        @media (max-width: 768px) {
            .comma-separator-widget-container { margin: 10px; padding: 20px; }
            .comma-separator-widget-title { font-size: 1.75rem; }
            .comma-separator-widget-description { font-size: 1rem; margin-bottom: 20px; }
            .comma-separator-buttons { flex-direction: column; gap: 12px; }
            .comma-separator-btn { flex: none; min-width: auto; width: 100%; }
            .comma-separator-options { grid-template-columns: 1fr; gap: 12px; }
            .comma-separator-textarea { padding: 15px; font-size: 16px; }
            .comma-separator-output { padding: 15px; font-size: 14px; }
            .comma-separator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .comma-separator-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .comma-separator-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .comma-separator-related-tool-name { font-size: 0.875rem; }
            .comma-separator-features-list { columns: 1; -webkit-columns: 1; -moz-columns: 1; }
        }

        @media (max-width: 480px) {
            .comma-separator-widget-container { margin: 5px; padding: 15px; }
            .comma-separator-widget-title { font-size: 1.5rem; }
            .comma-separator-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .comma-separator-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .comma-separator-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .comma-separator-related-tool-name { font-size: 0.75rem; }
            .comma-separator-notification { top: 10px; right: 10px; left: 10px; transform: translateY(-100px); text-align: center; }
            .comma-separator-notification.show { transform: translateY(0); }
        }

        [data-theme="dark"] .comma-separator-textarea:focus,
        [data-theme="dark"] .comma-separator-input:focus { box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); }
        .comma-separator-radio:focus, .comma-separator-btn:focus, .comma-separator-input:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .comma-separator-output::selection { background-color: var(--primary-color); color: white; }
    </style>
</head>
<body>
    <div class="comma-separator-widget-container">
        <h1 class="comma-separator-widget-title">Comma Separator</h1>
        <p class="comma-separator-widget-description">
            Add or remove commas from lists and convert between different list formats. Perfect for data formatting, CSV preparation, and list management.
        </p>
        
        <div class="comma-separator-input-group">
            <label for="commaSeparatorInput" class="comma-separator-label">Enter your list:</label>
            <textarea 
                id="commaSeparatorInput" 
                class="comma-separator-textarea"
                placeholder="Enter your list items (one per line or comma-separated)..."
                rows="4"
            ></textarea>
        </div>

        <div class="comma-separator-options">
            <div class="comma-separator-option">
                <input type="radio" id="addCommas" name="operationType" class="comma-separator-radio" value="add" checked>
                <label for="addCommas" class="comma-separator-option-label">Add Commas</label>
            </div>
            <div class="comma-separator-option">
                <input type="radio" id="removeCommas" name="operationType" class="comma-separator-radio" value="remove">
                <label for="removeCommas" class="comma-separator-option-label">Remove Commas</label>
            </div>
            <div class="comma-separator-option">
                <input type="radio" id="customSeparator" name="operationType" class="comma-separator-radio" value="custom">
                <label for="customSeparator" class="comma-separator-option-label">Custom Separator</label>
            </div>
            <div class="comma-separator-control-group" id="customSeparatorGroup" style="display: none;">
                <label for="customSeparatorInput" class="comma-separator-label">Custom Separator:</label>
                <input type="text" id="customSeparatorInput" class="comma-separator-input" placeholder="Enter separator (e.g., |, ;, -)">
            </div>
        </div>

        <div class="comma-separator-buttons">
            <button class="comma-separator-btn comma-separator-btn-primary" onclick="CommaSeparator.process()">
                Process List
            </button>
            <button class="comma-separator-btn comma-separator-btn-secondary" onclick="CommaSeparator.clear()">
                Clear All
            </button>
            <button class="comma-separator-btn comma-separator-btn-success" onclick="CommaSeparator.copy()">
                Copy Result
            </button>
        </div>

        <div class="comma-separator-result">
            <h3 class="comma-separator-result-title">Processed List:</h3>
            <div class="comma-separator-output" id="commaSeparatorOutput">
                Your processed list will appear here...
            </div>
        </div>

        <div class="comma-separator-related-tools">
            <h3 class="comma-separator-related-tools-title">Related Tools</h3>
            <div class="comma-separator-related-tools-grid">
                <a href="/p/text-to-slug_30.html" class="comma-separator-related-tool-item" rel="noopener">
                    <div class="comma-separator-related-tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="comma-separator-related-tool-name">Text to Slug</div>
                </a>

                <a href="/p/case-converter.html" class="comma-separator-related-tool-item" rel="noopener">
                    <div class="comma-separator-related-tool-icon">
                        <i class="fas fa-text-height"></i>
                    </div>
                    <div class="comma-separator-related-tool-name">Case Converter</div>
                </a>

                <a href="/p/word-counter.html" class="comma-separator-related-tool-item" rel="noopener">
                    <div class="comma-separator-related-tool-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="comma-separator-related-tool-name">Word Counter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Effortlessly Format Lists with Our Comma Separator Tool</h2>
            <p>The <strong>Comma Separator</strong> is an essential utility for anyone who works with lists and data. Whether you need to convert a column of items from a spreadsheet into a single, comma-separated line for a database query, or transform a list of tags into a newline-separated format for easier reading, this tool automates the entire process. It intelligently adds, removes, or replaces delimiters, saving you from the tedious task of manually editing each item. This is perfect for developers, data analysts, writers, and anyone needing to reformat lists quickly.</p>
            
            <h3>How to Use the Comma Separator</h3>
            <p>Our tool makes list formatting simple and fast:</p>
            <ol>
                <li><strong>Paste Your List:</strong> Enter your list into the input box. The tool can handle items separated by commas or new lines.</li>
                <li><strong>Select an Operation:</strong> Choose whether you want to 'Add Commas' (to convert a newline list to a comma-separated one), 'Remove Commas' (to convert a comma-separated list to a newline one), or use a 'Custom Separator'.</li>
                <li><strong>Process and Copy:</strong> The tool automatically processes your list. Once you have the desired format, click "Copy Result" to use it anywhere.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Comma Separator</h3>
            
            <h4>What is a comma separator used for?</h4>
            <p>A comma separator is a character (the comma) used to distinguish between different items in a text string. Its most common application is in CSV (Comma-Separated Values) files, which are a standard format for transferring data between different software applications, such as from Microsoft Excel to a database. It's also used for lists of keywords, tags, or email recipients.</p>
            
            <h4>How do I add commas between words or numbers?</h4>
            <p>The easiest method is to use an online tool. Simply paste your list of words or numbers (with each item on a new line) into the Comma Separator, select the 'Add Commas' option, and it will automatically generate a single string with each item separated by a comma and a space.</p>
            
            <h4>Can I automatically insert commas in a list?</h4>
            <p>Yes. This tool is designed to automatically insert commas into a list that is formatted with new lines (e.g., from a spreadsheet column). It takes your vertical list and converts it into a horizontal, comma-delimited format instantly, saving significant time and effort.</p>
            
            <h4>How do I separate values using a comma?</h4>
            <p>To separate values with a comma, you can copy your list from any source (like a text document or a column in Excel) and paste it into this tool. The tool will then join each line or item with a comma, creating a properly formatted, comma-separated list.</p>
            
            <h4>What is the difference between comma and semicolon separators?</h4>
            <p>While both are used to separate items, their usage often depends on context and regional standards. A comma is the most widely accepted delimiter in the United States for CSV files. A semicolon is frequently used in Europe and other regions where the comma is reserved as a decimal separator (e.g., 1.234,56). Semicolons are also useful for separating list items that themselves contain commas.</p>
        </div>

        <div class="comma-separator-features">
            <h3 class="comma-separator-features-title">Key Features:</h3>
            <ul class="comma-separator-features-list">
                <li class="comma-separator-features-item">Add commas to line-separated lists</li>
                <li class="comma-separator-features-item">Remove commas from comma-separated lists</li>
                <li class="comma-separator-features-item">Convert to custom separators</li>
                <li class="comma-separator-features-item">Smart list detection</li>
                <li class="comma-separator-features-item">Preserve formatting options</li>
                <li class="comma-separator-features-item">Real-time preview</li>
                <li class="comma-separator-features-item">One-click copy to clipboard</li>
                <li class="comma-separator-features-item">Mobile-responsive design</li>
                <li class="comma-separator-features-item">No data sent to servers</li>
            </ul>
        </div>
    </div>

    <!-- Copy notification -->
    <div class="comma-separator-notification" id="commaSeparatorNotification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Comma Separator Tool
        (function() {
            'use strict';

            const elements = {
                input: () => document.getElementById('commaSeparatorInput'),
                output: () => document.getElementById('commaSeparatorOutput'),
                notification: () => document.getElementById('commaSeparatorNotification'),
                customSeparatorInput: () => document.getElementById('customSeparatorInput'),
                customSeparatorGroup: () => document.getElementById('customSeparatorGroup')
            };

            window.CommaSeparator = {
                process() {
                    const input = elements.input();
                    const output = elements.output();
                    const text = input.value;

                    if (!text.trim()) {
                        output.textContent = 'Please enter some text to process.';
                        output.style.color = '#dc2626';
                        return;
                    }

                    output.style.color = '';
                    const operationType = document.querySelector('input[name="operationType"]:checked').value;
                    const processedText = this.processText(text, operationType);
                    output.textContent = processedText;
                },

                processText(text, operationType) {
                    let items = [];

                    // Parse input based on current format
                    if (text.includes(',')) {
                        // Comma-separated input
                        items = text.split(',').map(item => item.trim()).filter(item => item !== '');
                    } else {
                        // Line-separated input
                        items = text.split('\n').map(item => item.trim()).filter(item => item !== '');
                    }

                    if (items.length === 0) {
                        return 'No valid items found to process.';
                    }

                    // Process based on operation type
                    switch (operationType) {
                        case 'add':
                            return items.join(', ');
                        case 'remove':
                            return items.join('\n');
                        case 'custom':
                            const customSep = elements.customSeparatorInput().value || ', ';
                            return items.join(customSep);
                        default:
                            return items.join(', ');
                    }
                },

                clear() {
                    elements.input().value = '';
                    elements.output().textContent = 'Your processed list will appear here...';
                    elements.output().style.color = '';
                    document.getElementById('addCommas').checked = true;
                    elements.customSeparatorInput().value = '';
                    this.toggleCustomSeparator();
                },

                copy() {
                    const text = elements.output().textContent;
                    if (['Your processed list will appear here...', 'Please enter some text to process.', 'No valid items found to process.'].includes(text)) return;

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(text).then(() => this.showNotification()).catch(() => this.fallbackCopy(text));
                    } else {
                        this.fallbackCopy(text);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                    }
                    document.body.removeChild(textArea);
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                },

                toggleCustomSeparator() {
                    const operationType = document.querySelector('input[name="operationType"]:checked').value;
                    const customGroup = elements.customSeparatorGroup();
                    customGroup.style.display = operationType === 'custom' ? 'flex' : 'none';
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const input = elements.input();
                const operationOptions = document.querySelectorAll('input[name="operationType"]');
                const customSeparatorInput = elements.customSeparatorInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // Auto-process on input changes
                input.addEventListener('input', function() {
                    if (this.value.trim()) {
                        CommaSeparator.process();
                    } else {
                        elements.output().textContent = 'Your processed list will appear here...';
                        elements.output().style.color = '';
                    }
                });

                // Auto-process when operation type changes
                operationOptions.forEach(option => {
                    option.addEventListener('change', () => {
                        CommaSeparator.toggleCustomSeparator();
                        if (input.value.trim()) CommaSeparator.process();
                    });
                });

                // Auto-process when custom separator changes
                customSeparatorInput.addEventListener('input', () => {
                    if (input.value.trim()) CommaSeparator.process();
                });

                // Handle Ctrl+Enter
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        e.preventDefault();
                        CommaSeparator.process();
                    }
                });
            });
        })();
    </script>
</body>
</html>