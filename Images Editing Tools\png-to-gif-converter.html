<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PNG to GIF Converter - Free Online Image Format Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free PNG to GIF Converter - Convert Images Online",
        "description": "Convert PNG images to GIF format instantly. Free online tool with transparency support, animation capabilities, and web-optimized output.",
        "url": "https://www.webtoolskit.org/p/png-to-gif.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-22",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "PNG to GIF Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert PNG to GIF" },
            { "@type": "DownloadAction", "name": "Download Converted GIF" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "Can we convert PNG to GIF?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, you can easily convert PNG to GIF using our free online converter. The conversion preserves transparency and creates web-compatible GIF files. However, GIF is limited to 256 colors, so complex PNG images may experience some color reduction during conversion."
          }
        },
        {
          "@type": "Question",
          "name": "What is the difference between PNG and GIF?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "PNG supports millions of colors with lossless compression and alpha transparency, making it ideal for detailed images. GIF is limited to 256 colors but supports animation and has smaller file sizes for simple graphics. PNG is better for photos, while GIF excels at simple graphics and animations."
          }
        },
        {
          "@type": "Question",
          "name": "Is PNG good for animations?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "PNG itself doesn't support animation, but APNG (Animated PNG) does. However, APNG has limited browser support compared to GIF. For web animations, GIF remains the most compatible choice, while APNG offers better quality but less universal support."
          }
        },
        {
          "@type": "Question",
          "name": "Were PNGs made to replace GIFs?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "PNG was created to replace GIF for static images due to patent issues with GIF compression and to provide better features like true color support and alpha transparency. However, PNG doesn't support animation, so GIF remains essential for animated graphics on the web."
          }
        },
        {
          "@type": "Question",
          "name": "Is animated PNG better than GIF?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "APNG (Animated PNG) offers better quality with millions of colors and true alpha transparency compared to GIF's 256 colors. However, GIF has universal browser support and smaller file sizes for simple animations. Choose APNG for quality, GIF for compatibility."
          }
        }
      ]
    }
    </script>

    <style>
        /* PNG to GIF Widget - Simplified & Template Compatible */
        .png-gif-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .png-gif-widget-container * { box-sizing: border-box; }

        .png-gif-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .png-gif-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .png-gif-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-lg);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
        }

        .png-gif-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .png-gif-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .png-gif-upload-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .png-gif-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .png-gif-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .png-gif-file-input {
            display: none;
        }

        .png-gif-color-warning {
            background-color: #fef3cd;
            border: 1px solid #fecaca;
            color: #92400e;
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-lg);
            font-size: 0.875rem;
            display: none;
        }

        .png-gif-preview {
            display: none;
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }

        .png-gif-preview-title {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .png-gif-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: flex-start;
        }

        .png-gif-preview-item {
            flex: 1;
            text-align: center;
        }

        .png-gif-preview-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-sm);
        }

        .png-gif-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-sm);
        }

        .png-gif-file-info {
            font-size: 0.875rem;
            color: var(--text-color-light);
        }

        .png-gif-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .png-gif-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .png-gif-btn:hover { transform: translateY(-2px); }

        .png-gif-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .png-gif-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .png-gif-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .png-gif-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .png-gif-btn-success {
            background-color: #10b981;
            color: white;
        }

        .png-gif-btn-success:hover {
            background-color: #059669;
        }

        .png-gif-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .png-gif-btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .png-gif-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="gif-to-png"] .png-gif-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .png-gif-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="png-to-jpg"] .png-gif-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .png-gif-related-tool-item:hover .png-gif-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="gif-to-png"]:hover .png-gif-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .png-gif-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="png-to-jpg"]:hover .png-gif-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .png-gif-related-tool-item { box-shadow: none; border: none; }
        .png-gif-related-tool-item:hover { box-shadow: none; border: none; }
        .png-gif-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .png-gif-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .png-gif-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .png-gif-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .png-gif-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .png-gif-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .png-gif-related-tool-item:hover .png-gif-related-tool-name { color: var(--primary-color); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .png-gif-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .png-gif-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .png-gif-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
            columns: 2;
            -webkit-columns: 2;
            -moz-columns: 2;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-top: 0;
            padding-bottom: 0;
        }

        .png-gif-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
            break-inside: avoid;
        }

        .png-gif-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        @media (max-width: 768px) {
            .png-gif-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .png-gif-widget-title { font-size: 1.875rem; }
            .png-gif-buttons { flex-direction: column; }
            .png-gif-btn { flex: none; }
            .png-gif-preview-content { flex-direction: column; }
            .png-gif-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .png-gif-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .png-gif-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .png-gif-related-tool-name { font-size: 0.875rem; }
            .png-gif-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; }
        }

        @media (max-width: 480px) {
            .png-gif-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .png-gif-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .png-gif-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .png-gif-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .png-gif-upload-area:hover { background-color: var(--card-bg); }
        .png-gif-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        [data-theme="dark"] .png-gif-color-warning { background-color: #451a03; border-color: #92400e; color: #fbbf24; }
    </style>
</head>
<body>
    <div class="png-gif-widget-container">
        <h1 class="png-gif-widget-title">PNG to GIF Converter</h1>
        <p class="png-gif-widget-description">
            Convert PNG images to GIF format with transparency support and web compatibility. Perfect for simple graphics and web optimization.
        </p>
        
        <div class="png-gif-upload-area" id="uploadArea">
            <div class="png-gif-upload-icon">📁</div>
            <div class="png-gif-upload-text">Click to select PNG image or drag & drop</div>
            <div class="png-gif-upload-subtext">Supports PNG files (Max 10MB)</div>
            <input type="file" id="fileInput" class="png-gif-file-input" accept=".png">
        </div>

        <div class="png-gif-color-warning" id="colorWarning">
            <strong>Color Limitation Notice:</strong> GIF format is limited to 256 colors. Complex PNG images with many colors may experience some color reduction during conversion.
        </div>

        <div class="png-gif-preview" id="previewSection">
            <h3 class="png-gif-preview-title">Preview & Comparison</h3>
            <div class="png-gif-preview-content">
                <div class="png-gif-preview-item">
                    <div class="png-gif-preview-label">Original PNG</div>
                    <img id="originalImage" class="png-gif-preview-image" alt="Original PNG" />
                    <div class="png-gif-file-info" id="originalInfo"></div>
                </div>
                <div class="png-gif-preview-item">
                    <div class="png-gif-preview-label">Converted GIF</div>
                    <img id="convertedImage" class="png-gif-preview-image" alt="Converted GIF" />
                    <div class="png-gif-file-info" id="convertedInfo"></div>
                </div>
            </div>
        </div>

        <div class="png-gif-buttons">
            <button id="convertBtn" class="png-gif-btn png-gif-btn-primary" disabled>
                Convert to GIF
            </button>
            <button id="downloadBtn" class="png-gif-btn png-gif-btn-success" disabled>
                Download GIF
            </button>
            <button id="resetBtn" class="png-gif-btn png-gif-btn-secondary">
                Reset
            </button>
        </div>

        <div class="png-gif-related-tools">
            <h3 class="png-gif-related-tools-title">Related Tools</h3>
            <div class="png-gif-related-tools-grid">
                <a href="https://www.webtoolskit.org/p/gif-to-png.html" class="png-gif-related-tool-item" rel="noopener">
                    <div class="png-gif-related-tool-icon">
                        <i class="fas fa-undo-alt"></i>
                    </div>
                    <div class="png-gif-related-tool-name">GIF to PNG</div>
                </a>

                <a href="https://www.webtoolskit.org/p/image-converter_23.html" class="png-gif-related-tool-item" rel="noopener">
                    <div class="png-gif-related-tool-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="png-gif-related-tool-name">Image Converter</div>
                </a>

                <a href="https://www.webtoolskit.org/p/png-to-jpg.html" class="png-gif-related-tool-item" rel="noopener">
                    <div class="png-gif-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="png-gif-related-tool-name">PNG to JPG</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert PNG to GIF Online - Web-Compatible Graphics</h2>
            <p>Our <strong>PNG to GIF Converter</strong> transforms high-quality PNG images into web-compatible GIF format, perfect for simple graphics, logos, and images that need broad browser support. While GIF is limited to 256 colors, it offers excellent compatibility and smaller file sizes for simple graphics.</p>

            <p>Converting PNG to GIF is ideal when you need maximum web compatibility, smaller file sizes for simple graphics, or when working with older systems that may not fully support PNG. Our tool processes conversions locally, ensuring your images remain private while delivering optimized results.</p>

            <h3>Frequently Asked Questions About PNG to GIF Conversion</h3>

            <h4>Can we convert PNG to GIF?</h4>
            <p>Yes, you can easily convert PNG to GIF using our free online converter. The conversion preserves transparency and creates web-compatible GIF files. However, GIF is limited to 256 colors, so complex PNG images may experience some color reduction during conversion.</p>

            <h4>What is the difference between PNG and GIF?</h4>
            <p>PNG supports millions of colors with lossless compression and alpha transparency, making it ideal for detailed images. GIF is limited to 256 colors but supports animation and has smaller file sizes for simple graphics. PNG is better for photos, while GIF excels at simple graphics and animations.</p>

            <h4>Is PNG good for animations?</h4>
            <p>PNG itself doesn't support animation, but APNG (Animated PNG) does. However, APNG has limited browser support compared to GIF. For web animations, GIF remains the most compatible choice, while APNG offers better quality but less universal support.</p>

            <h4>Were PNGs made to replace GIFs?</h4>
            <p>PNG was created to replace GIF for static images due to patent issues with GIF compression and to provide better features like true color support and alpha transparency. However, PNG doesn't support animation, so GIF remains essential for animated graphics on the web.</p>

            <h4>Is animated PNG better than GIF?</h4>
            <p>APNG (Animated PNG) offers better quality with millions of colors and true alpha transparency compared to GIF's 256 colors. However, GIF has universal browser support and smaller file sizes for simple animations. Choose APNG for quality, GIF for compatibility.</p>
        </div>

        <div class="png-gif-features">
            <h3 class="png-gif-features-title">Key Features</h3>
            <ul class="png-gif-features-list">
                <li class="png-gif-features-item">Convert PNG to GIF instantly</li>
                <li class="png-gif-features-item">Transparency preservation</li>
                <li class="png-gif-features-item">Universal web compatibility</li>
                <li class="png-gif-features-item">Optimized for simple graphics</li>
                <li class="png-gif-features-item">Client-side processing for privacy</li>
                <li class="png-gif-features-item">Smaller file sizes</li>
                <li class="png-gif-features-item">Real-time preview</li>
                <li class="png-gif-features-item">Legacy browser support</li>
            </ul>
        </div>
    </div>

    <script>
        // PNG to GIF Converter Tool - Self-contained IIFE
        (function() {
            'use strict';

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                colorWarning: () => document.getElementById('colorWarning'),
                previewSection: () => document.getElementById('previewSection'),
                originalImage: () => document.getElementById('originalImage'),
                convertedImage: () => document.getElementById('convertedImage'),
                originalInfo: () => document.getElementById('originalInfo'),
                convertedInfo: () => document.getElementById('convertedInfo'),
                convertBtn: () => document.getElementById('convertBtn'),
                downloadBtn: () => document.getElementById('downloadBtn'),
                resetBtn: () => document.getElementById('resetBtn')
            };

            let originalFile = null;
            let convertedBlob = null;

            function init() {
                setupEventListeners();
            }

            function setupEventListeners() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();
                const convertBtn = elements.convertBtn();
                const downloadBtn = elements.downloadBtn();
                const resetBtn = elements.resetBtn();

                // File upload events
                uploadArea.addEventListener('click', () => fileInput.click());
                fileInput.addEventListener('change', handleFileSelect);

                // Drag and drop events
                uploadArea.addEventListener('dragover', handleDragOver);
                uploadArea.addEventListener('dragleave', handleDragLeave);
                uploadArea.addEventListener('drop', handleDrop);

                // Button events
                convertBtn.addEventListener('click', convertImage);
                downloadBtn.addEventListener('click', downloadImage);
                resetBtn.addEventListener('click', resetTool);
            }

            function handleFileSelect(event) {
                const file = event.target.files[0];
                if (file) processFile(file);
            }

            function handleDragOver(event) {
                event.preventDefault();
                elements.uploadArea().classList.add('dragover');
            }

            function handleDragLeave(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
            }

            function handleDrop(event) {
                event.preventDefault();
                elements.uploadArea().classList.remove('dragover');
                const files = event.dataTransfer.files;
                if (files.length > 0) processFile(files[0]);
            }

            function processFile(file) {
                if (!file.type.includes('png')) {
                    alert('Please select a PNG image file.');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    alert('File size must be less than 10MB.');
                    return;
                }

                originalFile = file;
                displayOriginalImage();
                elements.convertBtn().disabled = false;
                elements.colorWarning().style.display = 'block';
            }

            function displayOriginalImage() {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const originalImage = elements.originalImage();
                    originalImage.src = e.target.result;
                    
                    const originalInfo = elements.originalInfo();
                    originalInfo.textContent = `${originalFile.name} (${formatFileSize(originalFile.size)})`;
                    
                    elements.previewSection().style.display = 'block';
                };
                reader.readAsDataURL(originalFile);
            }

            function convertImage() {
                if (!originalFile) return;

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = () => {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    // Convert to GIF format (note: browser support for GIF creation is limited)
                    // This will create a static GIF from the PNG
                    canvas.toBlob((blob) => {
                        convertedBlob = blob;
                        displayConvertedImage();
                        elements.downloadBtn().disabled = false;
                    }, 'image/gif');
                };

                img.src = URL.createObjectURL(originalFile);
            }

            function displayConvertedImage() {
                const convertedImage = elements.convertedImage();
                convertedImage.src = URL.createObjectURL(convertedBlob);
                
                const convertedInfo = elements.convertedInfo();
                const fileName = originalFile.name.replace(/\.[^/.]+$/, '') + '.gif';
                const sizeChange = convertedBlob.size > originalFile.size ? 'increase' : 'decrease';
                const sizeRatio = ((convertedBlob.size / originalFile.size) * 100).toFixed(1);
                convertedInfo.innerHTML = `${fileName} (${formatFileSize(convertedBlob.size)})<br><small style="color: ${sizeChange === 'increase' ? '#dc3545' : '#28a745'};">Size ${sizeChange}: ${sizeRatio}%</small>`;
            }

            function downloadImage() {
                if (!convertedBlob) return;

                const link = document.createElement('a');
                link.href = URL.createObjectURL(convertedBlob);
                link.download = originalFile.name.replace(/\.[^/.]+$/, '') + '.gif';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            function resetTool() {
                originalFile = null;
                convertedBlob = null;
                elements.fileInput().value = '';
                elements.previewSection().style.display = 'none';
                elements.colorWarning().style.display = 'none';
                elements.convertBtn().disabled = true;
                elements.downloadBtn().disabled = true;
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Initialize when DOM is ready
            document.addEventListener('DOMContentLoaded', init);
        })();
    </script>
</body>
</html>