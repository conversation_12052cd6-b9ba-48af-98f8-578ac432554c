<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JPG to PNG Converter - Free Online Image Format Converter</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free JPG to PNG Converter - Convert Images Online",
        "description": "Convert JPG images to PNG format instantly. Free online tool with transparency support, high quality output, and batch conversion capabilities.",
        "url": "https://www.webtoolskit.org/p/jpg-to-png.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-15",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "JPG to PNG Converter",
            "applicationCategory": "MultimediaApplication",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert JPG to PNG" },
            { "@type": "DownloadAction", "name": "Download Converted PNG" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How to convert JPG to PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Simply upload your JPG file using our converter, and it will automatically convert it to PNG format. Click the download button to save your converted PNG image with transparency support and lossless quality."
          }
        },
        {
          "@type": "Question",
          "name": "How to convert JPG to PNG without background?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Converting JPG to PNG preserves the original image content. To remove backgrounds, you'll need a separate background removal tool first, then convert the result to PNG to maintain transparency."
          }
        },
        {
          "@type": "Question",
          "name": "Does converting JPG to PNG lose quality?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "No, converting JPG to PNG doesn't lose quality. PNG is a lossless format, so the conversion maintains the original image quality while adding support for transparency."
          }
        },
        {
          "@type": "Question",
          "name": "Should you convert JPG to PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Convert JPG to PNG when you need transparency support, lossless quality, or better compression for images with few colors. Keep JPG for photographs where file size matters more than transparency."
          }
        },
        {
          "@type": "Question",
          "name": "How do I turn a JPG image to PNG?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Use our free online converter: upload your JPG file, wait for automatic conversion, then download the PNG result. No software installation required - works directly in your browser."
          }
        }
      ]
    }
    </script>

    <style>
        /* JPG to PNG Widget - Simplified & Template Compatible */
        .jpg-png-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .jpg-png-widget-container * { box-sizing: border-box; }

        .jpg-png-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .jpg-png-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .jpg-png-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            margin-bottom: var(--spacing-xl);
            cursor: pointer;
        }

        .jpg-png-upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--card-bg);
        }

        .jpg-png-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .jpg-png-upload-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .jpg-png-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .jpg-png-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.875rem;
        }

        .jpg-png-file-input {
            display: none;
        }

        .jpg-png-preview {
            display: none;
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            border: 1px solid var(--border-color);
        }

        .jpg-png-preview-title {
            color: var(--text-color);
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            text-align: center;
        }

        .jpg-png-preview-content {
            display: flex;
            gap: var(--spacing-lg);
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .jpg-png-preview-item {
            text-align: center;
            flex: 1;
            min-width: 200px;
        }

        .jpg-png-preview-label {
            color: var(--text-color-light);
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .jpg-png-preview-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .jpg-png-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .jpg-png-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .jpg-png-btn:hover { transform: translateY(-2px); }

        .jpg-png-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .jpg-png-btn-primary:hover {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .jpg-png-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .jpg-png-btn-secondary:hover {
            background-color: var(--border-color);
        }

        .jpg-png-btn-success {
            background-color: #10b981;
            color: white;
        }

        .jpg-png-btn-success:hover {
            background-color: #059669;
        }

        .jpg-png-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .jpg-png-btn:disabled:hover {
            transform: none;
        }

        .jpg-png-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .jpg-png-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }

        .jpg-png-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .jpg-png-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .jpg-png-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .jpg-png-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .jpg-png-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .jpg-png-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="png-to-jpg"] .jpg-png-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="image-converter"] .jpg-png-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="image-resizer"] .jpg-png-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        .jpg-png-related-tool-item:hover .jpg-png-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        a[href*="png-to-jpg"]:hover .jpg-png-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="image-converter"]:hover .jpg-png-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="image-resizer"]:hover .jpg-png-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .jpg-png-related-tool-item { box-shadow: none; border: none; }
        .jpg-png-related-tool-item:hover { box-shadow: none; border: none; }
        .jpg-png-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .jpg-png-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .jpg-png-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .jpg-png-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .jpg-png-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .jpg-png-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .jpg-png-related-tool-item:hover .jpg-png-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .jpg-png-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .jpg-png-widget-title { font-size: 1.875rem; }
            .jpg-png-buttons { flex-direction: column; }
            .jpg-png-btn { flex: none; }
            .jpg-png-preview-content { flex-direction: column; }
            .jpg-png-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .jpg-png-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .jpg-png-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .jpg-png-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .jpg-png-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .jpg-png-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .jpg-png-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .jpg-png-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .jpg-png-upload-area:hover { background-color: var(--card-bg); }
        .jpg-png-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .jpg-png-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .jpg-png-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="jpg-png-widget-container">
        <h1 class="jpg-png-widget-title">JPG to PNG Converter</h1>
        <p class="jpg-png-widget-description">
            Convert JPG images to PNG format instantly with transparency support and lossless quality. Fast, free, and secure online conversion.
        </p>
        
        <div class="jpg-png-upload-area" id="uploadArea">
            <div class="jpg-png-upload-icon">📁</div>
            <div class="jpg-png-upload-text">Click to upload or drag & drop your JPG file</div>
            <div class="jpg-png-upload-subtext">Supports JPG, JPEG files up to 10MB</div>
            <input type="file" id="fileInput" class="jpg-png-file-input" accept=".jpg,.jpeg" />
        </div>

        <div class="jpg-png-preview" id="previewSection">
            <h3 class="jpg-png-preview-title">Image Preview</h3>
            <div class="jpg-png-preview-content">
                <div class="jpg-png-preview-item">
                    <div class="jpg-png-preview-label">Original (JPG)</div>
                    <img id="originalImage" class="jpg-png-preview-image" alt="Original JPG" />
                </div>
                <div class="jpg-png-preview-item">
                    <div class="jpg-png-preview-label">Converted (PNG)</div>
                    <img id="convertedImage" class="jpg-png-preview-image" alt="Converted PNG" />
                </div>
            </div>
        </div>

        <div class="jpg-png-buttons">
            <button id="convertBtn" class="jpg-png-btn jpg-png-btn-primary" disabled>
                Convert to PNG
            </button>
            <button id="downloadBtn" class="jpg-png-btn jpg-png-btn-success" disabled>
                Download PNG
            </button>
            <button id="resetBtn" class="jpg-png-btn jpg-png-btn-secondary">
                Reset
            </button>
        </div>

        <div class="jpg-png-related-tools">
            <h3 class="jpg-png-related-tools-title">Related Tools</h3>
            <div class="jpg-png-related-tools-grid">
                <a href="/p/png-to-jpg.html" class="jpg-png-related-tool-item" rel="noopener">
                    <div class="jpg-png-related-tool-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="jpg-png-related-tool-name">PNG to JPG</div>
                </a>

                <a href="/p/image-converter.html" class="jpg-png-related-tool-item" rel="noopener">
                    <div class="jpg-png-related-tool-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="jpg-png-related-tool-name">Image Converter</div>
                </a>

                <a href="/p/image-resizer.html" class="jpg-png-related-tool-item" rel="noopener">
                    <div class="jpg-png-related-tool-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div class="jpg-png-related-tool-name">Image Resizer</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Free JPG to PNG Converter Online</h2>
            <p>Transform your JPG images to PNG format effortlessly with our free online converter. PNG format offers lossless compression and transparency support, making it perfect for graphics, logos, and images requiring transparent backgrounds.</p>
            
            <p>Converting JPG to PNG maintains image quality while adding transparency capabilities. Our tool processes conversions instantly in your browser without uploading files to servers, ensuring complete privacy and security.</p>

            <h3>Why Convert JPG to PNG?</h3>
            <p>PNG format provides superior quality for images with sharp edges, text, or transparent elements. Unlike JPG's lossy compression, PNG preserves every pixel, making it ideal for professional graphics and web design projects.</p>

            <p>Use PNG when you need transparency support, lossless quality, or better compression for images with limited colors. The conversion process is instant and maintains the original image dimensions and quality.</p>

            <h3>Frequently Asked Questions About JPG to PNG Conversion</h3>
            
            <h4>How to convert JPG to PNG?</h4>
            <p>Simply upload your JPG file using our converter, and it will automatically convert it to PNG format. Click the download button to save your converted PNG image with transparency support and lossless quality. The process is instant and works directly in your browser.</p>
            
            <h4>How to convert JPG to PNG without background?</h4>
            <p>Converting JPG to PNG preserves the original image content, including any existing background. To remove backgrounds, you'll need a separate background removal tool first, then convert the result to PNG to maintain transparency. PNG format supports transparent backgrounds, making it perfect for logos and graphics.</p>
            
            <h4>Does converting JPG to PNG lose quality?</h4>
            <p>No, converting JPG to PNG doesn't lose quality. PNG is a lossless format, so the conversion maintains the original image quality while adding support for transparency. However, the file size may increase due to PNG's uncompressed nature.</p>
            
            <h4>Should you convert JPG to PNG?</h4>
            <p>Convert JPG to PNG when you need transparency support, lossless quality, or better compression for images with few colors. Keep JPG for photographs where file size matters more than transparency. PNG is ideal for logos, graphics, and images requiring transparent backgrounds.</p>
            
            <h4>How do I turn a JPG image to PNG?</h4>
            <p>Use our free online converter: upload your JPG file, wait for automatic conversion, then download the PNG result. No software installation required - works directly in your browser with complete privacy and security.</p>
        </div>

        <div class="jpg-png-features">
            <h3 class="jpg-png-features-title">Key Features</h3>
            <ul class="jpg-png-features-list">
                <li class="jpg-png-features-item">Instant JPG to PNG conversion</li>
                <li class="jpg-png-features-item">Lossless quality preservation</li>
                <li class="jpg-png-features-item">Transparency support</li>
                <li class="jpg-png-features-item">No file size limits</li>
                <li class="jpg-png-features-item">Secure browser-based processing</li>
                <li class="jpg-png-features-item">No software installation required</li>
                <li class="jpg-png-features-item">Drag and drop interface</li>
                <li class="jpg-png-features-item">Mobile-friendly design</li>
            </ul>
        </div>
    </div>

    <div id="notification" class="jpg-png-notification"></div>

    <script>
        (function() {
            'use strict';
            
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const previewSection = document.getElementById('previewSection');
            const originalImage = document.getElementById('originalImage');
            const convertedImage = document.getElementById('convertedImage');
            const convertBtn = document.getElementById('convertBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            const resetBtn = document.getElementById('resetBtn');
            const notification = document.getElementById('notification');
            
            let currentFile = null;
            let convertedBlob = null;
            
            // Upload area events
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            
            // File input change
            fileInput.addEventListener('change', handleFileSelect);
            
            // Button events
            convertBtn.addEventListener('click', convertImage);
            downloadBtn.addEventListener('click', downloadImage);
            resetBtn.addEventListener('click', resetTool);
            
            function handleDragOver(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            }
            
            function handleDragLeave(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            }
            
            function handleDrop(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    processFile(files[0]);
                }
            }
            
            function handleFileSelect(e) {
                const file = e.target.files[0];
                if (file) {
                    processFile(file);
                }
            }
            
            function processFile(file) {
                if (!file.type.match(/^image\/(jpeg|jpg)$/i)) {
                    showNotification('Please select a valid JPG file.', 'error');
                    return;
                }
                
                if (file.size > 10 * 1024 * 1024) {
                    showNotification('File size must be less than 10MB.', 'error');
                    return;
                }
                
                currentFile = file;
                displayPreview(file);
                convertBtn.disabled = false;
            }
            
            function displayPreview(file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    originalImage.src = e.target.result;
                    previewSection.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
            
            function convertImage() {
                if (!currentFile) return;
                
                convertBtn.textContent = 'Converting...';
                convertBtn.disabled = true;
                
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    // Draw image on canvas
                    ctx.drawImage(img, 0, 0);
                    
                    // Convert to PNG blob
                    canvas.toBlob(function(blob) {
                        convertedBlob = blob;
                        
                        // Display converted image
                        const url = URL.createObjectURL(blob);
                        convertedImage.src = url;
                        
                        // Enable download button
                        downloadBtn.disabled = false;
                        convertBtn.textContent = 'Convert to PNG';
                        
                        showNotification('Image converted successfully!', 'success');
                    }, 'image/png', 1.0);
                };
                
                img.src = originalImage.src;
            }
            
            function downloadImage() {
                if (!convertedBlob) return;
                
                const url = URL.createObjectURL(convertedBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = getFileName(currentFile.name) + '.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                showNotification('PNG file downloaded!', 'success');
            }
            
            function resetTool() {
                currentFile = null;
                convertedBlob = null;
                fileInput.value = '';
                previewSection.style.display = 'none';
                convertBtn.disabled = true;
                downloadBtn.disabled = true;
                convertBtn.textContent = 'Convert to PNG';
                uploadArea.classList.remove('dragover');
            }
            
            function getFileName(fullName) {
                return fullName.substring(0, fullName.lastIndexOf('.')) || fullName;
            }
            
            function showNotification(message, type = 'success') {
                notification.textContent = message;
                notification.className = 'jpg-png-notification show';
                
                if (type === 'error') {
                    notification.style.backgroundColor = '#dc3545';
                } else {
                    notification.style.backgroundColor = '#10b981';
                }
                
                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
        })();
    </script>
</body>
</html>