<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image to Base64 Converter - Free Online Base64 Encoder</title>

    <!-- Consolidated Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": ["WebPage", "SoftwareSourceCode"],
        "name": "Free Image to Base64 Converter - Encode Images to Base64 Strings",
        "description": "Convert images to Base64 encoded strings instantly. Perfect for embedding images in HTML, CSS, or generating data URIs for web development.",
        "url": "https://www.webtoolskit.org/p/image-to-base64.html",
        "programmingLanguage": ["HTML", "CSS", "JavaScript"],
        "isAccessibleForFree": true,
        "datePublished": "2025-06-01",
        "dateModified": "2025-06-21",
        "author": {
            "@type": "Organization",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        },
        "mainEntity": {
            "@type": "WebApplication",
            "name": "Image to Base64 Converter",
            "applicationCategory": "DeveloperTool",
            "operatingSystem": "Any",
            "offers": { "@type": "Offer", "price": "0", "priceCurrency": "USD" }
        },
        "potentialAction": [
            { "@type": "ConvertAction", "name": "Convert Image to Base64" },
            { "@type": "CopyAction", "name": "Copy Base64 String" }
        ]
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How do I convert an image to Base64?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Upload your image file using our converter tool, and it will instantly generate the Base64 encoded string. You can then copy the complete data URI or just the Base64 string for use in your HTML, CSS, or JavaScript code."
          }
        },
        {
          "@type": "Question",
          "name": "Why convert image to Base64?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Base64 encoding allows you to embed images directly in HTML, CSS, or email templates without external files. This reduces HTTP requests, prevents broken image links, and enables offline functionality. It's particularly useful for small icons, logos, and images in web applications."
          }
        },
        {
          "@type": "Question",
          "name": "What is the Base64 representation of an image?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Base64 representation of an image is a text string that encodes the binary image data using 64 ASCII characters. It typically starts with a data URI prefix like 'data:image/png;base64,' followed by the encoded image data. This allows images to be represented as text strings."
          }
        },
        {
          "@type": "Question",
          "name": "How to get Base64 from image URL?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "To get Base64 from an image URL, you first need to download the image from the URL, then convert it to Base64. Due to browser security restrictions (CORS), you cannot directly convert external URLs. Download the image first, then use our converter tool."
          }
        },
        {
          "@type": "Question",
          "name": "What are the disadvantages of Base64?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Base64 encoding increases file size by approximately 33%, making it inefficient for large images. It also makes HTML/CSS files larger, can't be cached separately by browsers, and may impact page loading performance. It's best used only for small images and icons."
          }
        }
      ]
    }
    </script>

    <style>
        /* Image to Base64 Converter Widget - Simplified & Template Compatible */
        .image-base64-widget-container {
            max-width: 800px;
            margin: var(--spacing-xl) auto;
            padding: var(--spacing-xl);
            background-color: var(--card-bg);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            font-family: var(--font-family);
            border: 1px solid var(--border-color);
        }

        .image-base64-widget-container * { box-sizing: border-box; }

        .image-base64-widget-title {
            color: var(--text-color);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            font-size: 2.25rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .image-base64-widget-description {
            text-align: center;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-xl);
            font-size: 1.125rem;
            line-height: 1.6;
        }

        .image-base64-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            margin-bottom: var(--spacing-xl);
            background-color: var(--background-color-alt);
            transition: var(--transition-base);
            cursor: pointer;
            position: relative;
            min-height: 160px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .image-base64-upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0, 71, 171, 0.05);
        }

        .image-base64-upload-icon {
            font-size: 3rem;
            color: var(--text-color-light);
            margin-bottom: var(--spacing-md);
        }

        .image-base64-upload-text {
            color: var(--text-color);
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .image-base64-upload-subtext {
            color: var(--text-color-light);
            font-size: 0.95rem;
        }

        .image-base64-file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .image-base64-preview {
            display: none;
            text-align: center;
            padding: var(--spacing-lg);
            background-color: var(--background-color-alt);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            margin-bottom: var(--spacing-xl);
        }

        .image-base64-preview.show { display: block; }

        .image-base64-preview-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.125rem;
            font-weight: 600;
        }

        .image-base64-preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            padding: var(--spacing-sm);
            margin: 0 auto var(--spacing-md);
            display: block;
        }

        .image-base64-preview-info {
            color: var(--text-color-light);
            font-size: 0.9rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }

        .image-base64-info-item {
            background: var(--card-bg);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--border-color);
        }

        .image-base64-buttons {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .image-base64-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-base);
            flex: 1;
            min-width: 140px;
        }

        .image-base64-btn:hover { transform: translateY(-2px); }

        .image-base64-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .image-base64-btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .image-base64-btn-primary:hover:not(:disabled) {
            background-color: var(--secondary-color);
            box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        }

        .image-base64-btn-secondary {
            background-color: var(--background-color-alt);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .image-base64-btn-secondary:hover:not(:disabled) {
            background-color: var(--border-color);
        }

        .image-base64-btn-success {
            background-color: #10b981;
            color: white;
        }

        .image-base64-btn-success:hover:not(:disabled) {
            background-color: #059669;
        }

        .image-base64-result {
            margin-bottom: var(--spacing-xl);
        }

        .image-base64-result-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.125rem;
            font-weight: 600;
        }

        .image-base64-output {
            background-color: var(--background-color-alt);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            min-height: 120px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            color: var(--text-color);
            white-space: pre-wrap;
            word-break: break-all;
            overflow-y: auto;
            max-height: 300px;
        }

        .image-base64-output:empty::before {
            content: "Your Base64 encoded image will appear here...";
            color: var(--text-color-light);
            font-style: italic;
        }

        .image-base64-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #10b981;
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(400px);
            transition: var(--transition-base);
        }

        .image-base64-notification.show { transform: translateX(0); }
        
        .seo-content {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
            color: var(--text-color-light);
            line-height: 1.7;
        }
        .seo-content h2, .seo-content h3, .seo-content h4 {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
        }
        .seo-content h2 { font-size: 1.75rem; font-weight: 700; margin-top: 0; }
        .seo-content h3 { font-size: 1.5rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content h4 { font-size: 1.25rem; font-weight: 600; margin-top: var(--spacing-lg); }
        .seo-content p, .seo-content ul, .seo-content ol { margin-bottom: var(--spacing-md); }
        .seo-content ul, .seo-content ol { padding-left: 20px; }
        .seo-content li { margin-bottom: var(--spacing-sm); }
        .seo-content code {
            background-color: var(--background-color-alt);
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            border-radius: 6px;
            font-family: 'SF Mono', Monaco, monospace;
        }

        .image-base64-features {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--border-color);
        }

        .image-base64-features-title {
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .image-base64-features-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .image-base64-features-item {
            padding: var(--spacing-sm) 0 var(--spacing-sm) var(--spacing-lg);
            color: var(--text-color-light);
            position: relative;
        }

        .image-base64-features-item:before {
            content: "";
            position: absolute;
            left: 0;
            top: calc(var(--spacing-sm) + 2px);
            width: 12px;
            height: 6px;
            border-left: 2px solid #10b981;
            border-bottom: 2px solid #10b981;
            transform: rotate(-45deg);
        }
        
        .image-base64-related-tool-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--spacing-sm);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.25rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
            border: none;
        }
        
        a[href*="base64-to-image"] .image-base64-related-tool-icon { background: linear-gradient(145deg, #007bff, #0056b3); }
        a[href*="ico-converter"] .image-base64-related-tool-icon { background: linear-gradient(145deg, #28a745, #1e7e34); }
        a[href*="image-converter"] .image-base64-related-tool-icon { background: linear-gradient(145deg, #6f42c1, #563d7c); }

        a[href*="base64-to-image"]:hover .image-base64-related-tool-icon { background: linear-gradient(145deg, #0088ff, #0062cc); }
        a[href*="ico-converter"]:hover .image-base64-related-tool-icon { background: linear-gradient(145deg, #2dbc4e, #218838); }
        a[href*="image-converter"]:hover .image-base64-related-tool-icon { background: linear-gradient(145deg, #855bcf, #62498a); }
        
        .image-base64-related-tool-item:hover .image-base64-related-tool-icon {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.18), inset 0 -2px 2px rgba(0, 0, 0, 0.15);
        }
        
        .image-base64-related-tool-item { box-shadow: none; border: none; }
        .image-base64-related-tool-item:hover { box-shadow: none; border: none; }
        .image-base64-related-tools { margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 1px solid var(--border-color); }
        .image-base64-related-tools-title { color: var(--text-color); margin-bottom: var(--spacing-xl); font-size: 1.5rem; font-weight: 700; text-align: center; }
        .image-base64-related-tools-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(140px, 1fr)); gap: var(--spacing-lg); margin-top: var(--spacing-lg); justify-items: center; }
        .image-base64-related-tool-item { text-align: center; text-decoration: none; color: inherit; transition: var(--transition-base); padding: var(--spacing-lg); border-radius: var(--border-radius-lg); display: block; width: 100%; max-width: 160px; }
        .image-base64-related-tool-item:hover { transform: translateY(0); background-color: transparent; }
        .image-base64-related-tool-name { font-size: 1rem; font-weight: 600; color: var(--text-color); margin-top: var(--spacing-sm); line-height: 1.3; }
        .image-base64-related-tool-item:hover .image-base64-related-tool-name { color: var(--primary-color); }
        
        @media (max-width: 768px) {
            .image-base64-widget-container { margin: var(--spacing-md); padding: var(--spacing-lg); }
            .image-base64-widget-title { font-size: 1.875rem; }
            .image-base64-buttons { flex-direction: column; }
            .image-base64-btn { flex: none; }
            .image-base64-upload-area { min-height: 140px; padding: var(--spacing-lg); }
            .image-base64-upload-icon { font-size: 2.5rem; }
            .image-base64-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-md); }
            .image-base64-related-tool-item { padding: var(--spacing-md); max-width: none; }
            .image-base64-related-tool-icon { width: 64px; height: 64px; font-size: 2rem; border-radius: 16px; }
            .image-base64-related-tool-name { font-size: 0.875rem; }
        }

        @media (max-width: 480px) {
            .image-base64-related-tools-grid { grid-template-columns: repeat(3, 1fr); gap: var(--spacing-sm); }
            .image-base64-related-tool-item { padding: var(--spacing-sm); max-width: none; }
            .image-base64-related-tool-icon { width: 56px; height: 56px; font-size: 1.75rem; border-radius: 12px; }
            .image-base64-related-tool-name { font-size: 0.75rem; }
        }

        [data-theme="dark"] .image-base64-upload-area.dragover { background-color: rgba(96, 165, 250, 0.1); }
        .image-base64-btn:focus { outline: 2px solid var(--primary-color); outline-offset: 2px; }
        .image-base64-output::selection { background-color: var(--primary-color); color: white; }
        .image-base64-features-list { columns: 2; -webkit-columns: 2; -moz-columns: 2; margin-top: 0.5em; margin-bottom: 0.5em; padding-top: 0; padding-bottom: 0; }
        @media (max-width: 600px) { .image-base64-features-list { columns: 1 !important; -webkit-columns: 1 !important; -moz-columns: 1 !important; } }
    </style>
</head>
<body>
    <div class="image-base64-widget-container">
        <h1 class="image-base64-widget-title">Image to Base64 Converter</h1>
        <p class="image-base64-widget-description">
            Convert images to Base64 encoded strings for embedding in HTML, CSS, or creating data URIs. Fast, secure, and works offline.
        </p>
        
        <div class="image-base64-upload-area" id="uploadArea">
            <input type="file" class="image-base64-file-input" id="fileInput" accept="image/*">
            <div class="image-base64-upload-icon">🖼️</div>
            <div class="image-base64-upload-text">Drop an image here or click to browse</div>
            <div class="image-base64-upload-subtext">Supports PNG, JPG, GIF, WebP, SVG, BMP</div>
        </div>

        <div class="image-base64-preview" id="preview">
            <div class="image-base64-preview-title">Preview:</div>
            <img class="image-base64-preview-image" id="previewImage" alt="Preview">
            <div class="image-base64-preview-info" id="previewInfo"></div>
        </div>

        <div class="image-base64-buttons">
            <button class="image-base64-btn image-base64-btn-primary" id="convertBtn" onclick="ImageBase64Converter.convert()" disabled>
                Convert to Base64
            </button>
            <button class="image-base64-btn image-base64-btn-secondary" onclick="ImageBase64Converter.clear()">
                Clear All
            </button>
            <button class="image-base64-btn image-base64-btn-success" onclick="ImageBase64Converter.copy()" disabled id="copyBtn">
                Copy Result
            </button>
        </div>

        <div class="image-base64-result">
            <h3 class="image-base64-result-title">Base64 Result:</h3>
            <div class="image-base64-output" id="base64Output"></div>
        </div>

        <div class="image-base64-related-tools">
            <h3 class="image-base64-related-tools-title">Related Tools</h3>
            <div class="image-base64-related-tools-grid">
                <a href="/p/base64-to-image.html" class="image-base64-related-tool-item" rel="noopener">
                    <div class="image-base64-related-tool-icon">
                        <i class="fas fa-file-image"></i>
                    </div>
                    <div class="image-base64-related-tool-name">Base64 to Image</div>
                </a>

                <a href="/p/ico-converter.html" class="image-base64-related-tool-item" rel="noopener">
                    <div class="image-base64-related-tool-icon">
                        <i class="fas fa-icons"></i>
                    </div>
                    <div class="image-base64-related-tool-name">ICO Converter</div>
                </a>

                <a href="/p/image-converter.html" class="image-base64-related-tool-item" rel="noopener">
                    <div class="image-base64-related-tool-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="image-base64-related-tool-name">Image Converter</div>
                </a>
            </div>
        </div>

        <div class="seo-content">
            <h2>Convert Images to Base64 Encoding for Web Development</h2>
            <p>Base64 encoding transforms binary image data into ASCII text strings, making it possible to embed images directly in HTML, CSS, or JavaScript code. Our <strong>Image to Base64 converter</strong> instantly generates data URIs that can be used to display images without external file references. This technique is particularly valuable for email templates, offline applications, and reducing HTTP requests in web development.</p>
            <p>When you convert an image to Base64, you get a text representation that starts with a data URI scheme like <code>data:image/png;base64,</code> followed by the encoded image data. This approach eliminates broken image links and ensures your images are always available, even in offline scenarios.</p>
            
            <h3>How to Convert Images to Base64</h3>
            <ol>
                <li><strong>Upload Image:</strong> Drag and drop your image file or click to browse. Supports all common formats including PNG, JPG, GIF, WebP, and SVG.</li>
                <li><strong>Preview & Verify:</strong> Review the uploaded image and its properties to ensure it's the correct file before conversion.</li>
                <li><strong>Convert & Copy:</strong> Click "Convert to Base64" to generate the encoded string. Use "Copy Result" to copy the complete data URI or just the Base64 data.</li>
            </ol>
        
            <h3>Frequently Asked Questions About Image to Base64</h3>
            
            <h4>How do I convert an image to Base64?</h4>
            <p>Upload your image file using our converter tool, and it will instantly generate the Base64 encoded string. You can then copy the complete data URI or just the Base64 string for use in your HTML, CSS, or JavaScript code.</p>
            
            <h4>Why convert image to Base64?</h4>
            <p>Base64 encoding allows you to embed images directly in HTML, CSS, or email templates without external files. This reduces HTTP requests, prevents broken image links, and enables offline functionality. It's particularly useful for small icons, logos, and images in web applications.</p>
            
            <h4>What is the Base64 representation of an image?</h4>
            <p>Base64 representation of an image is a text string that encodes the binary image data using 64 ASCII characters. It typically starts with a data URI prefix like 'data:image/png;base64,' followed by the encoded image data. This allows images to be represented as text strings.</p>
            
            <h4>How to get Base64 from image URL?</h4>
            <p>To get Base64 from an image URL, you first need to download the image from the URL, then convert it to Base64. Due to browser security restrictions (CORS), you cannot directly convert external URLs. Download the image first, then use our converter tool.</p>
            
            <h4>What are the disadvantages of Base64?</h4>
            <p>Base64 encoding increases file size by approximately 33%, making it inefficient for large images. It also makes HTML/CSS files larger, can't be cached separately by browsers, and may impact page loading performance. It's best used only for small images and icons.</p>
        </div>

        <div class="image-base64-features">
            <h3 class="image-base64-features-title">Key Features:</h3>
            <ul class="image-base64-features-list">
                <li class="image-base64-features-item" style="margin-bottom: 0.3em;">Instant Base64 encoding</li>
                <li class="image-base64-features-item" style="margin-bottom: 0.3em;">Multiple image format support</li>
                <li class="image-base64-features-item" style="margin-bottom: 0.3em;">Data URI generation</li>
                <li class="image-base64-features-item" style="margin-bottom: 0.3em;">One-click copy to clipboard</li>
                <li class="image-base64-features-item" style="margin-bottom: 0.3em;">Real-time preview</li>
                <li class="image-base64-features-item" style="margin-bottom: 0.3em;">Drag & drop interface</li>
                <li class="image-base64-features-item">No server upload required</li>
            </ul>
        </div>
    </div>

    <!-- Notification -->
    <div class="image-base64-notification" id="notification">
        ✓ Copied to clipboard!
    </div>

    <script>
        // Image to Base64 Converter - Self-contained IIFE
        (function() {
            'use strict';

            let currentBase64 = '';

            const elements = {
                uploadArea: () => document.getElementById('uploadArea'),
                fileInput: () => document.getElementById('fileInput'),
                preview: () => document.getElementById('preview'),
                previewImage: () => document.getElementById('previewImage'),
                previewInfo: () => document.getElementById('previewInfo'),
                base64Output: () => document.getElementById('base64Output'),
                convertBtn: () => document.getElementById('convertBtn'),
                copyBtn: () => document.getElementById('copyBtn'),
                notification: () => document.getElementById('notification')
            };

            window.ImageBase64Converter = {
                convert() {
                    const fileInput = elements.fileInput();
                    const file = fileInput.files[0];
                    
                    if (!file) {
                        alert('Please select an image file first.');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        currentBase64 = e.target.result;
                        elements.base64Output().textContent = currentBase64;
                        elements.copyBtn().disabled = false;
                    };
                    reader.onerror = () => {
                        alert('Error reading file. Please try again.');
                    };
                    reader.readAsDataURL(file);
                },

                copy() {
                    if (!currentBase64) {
                        alert('No Base64 data to copy. Please convert an image first.');
                        return;
                    }

                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(currentBase64).then(() => {
                            this.showNotification();
                        }).catch(() => {
                            this.fallbackCopy(currentBase64);
                        });
                    } else {
                        this.fallbackCopy(currentBase64);
                    }
                },

                fallbackCopy(text) {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.cssText = 'position:fixed;left:-999999px;top:-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        this.showNotification();
                    } catch (err) {
                        console.error('Failed to copy text: ', err);
                        alert('Could not copy to clipboard. Please select and copy manually.');
                    }
                    document.body.removeChild(textArea);
                },

                clear() {
                    elements.fileInput().value = '';
                    elements.preview().classList.remove('show');
                    elements.base64Output().textContent = '';
                    elements.convertBtn().disabled = true;
                    elements.copyBtn().disabled = true;
                    currentBase64 = '';
                },

                showNotification() {
                    const notification = elements.notification();
                    notification.classList.add('show');
                    setTimeout(() => notification.classList.remove('show'), 2500);
                },

                showPreview(file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        elements.previewImage().src = e.target.result;
                        elements.previewInfo().innerHTML = `
                            <div class="image-base64-info-item"><strong>Name:</strong> ${file.name}</div>
                            <div class="image-base64-info-item"><strong>Size:</strong> ${(file.size / 1024).toFixed(1)} KB</div>
                            <div class="image-base64-info-item"><strong>Type:</strong> ${file.type}</div>
                        `;
                        elements.preview().classList.add('show');
                    };
                    reader.readAsDataURL(file);
                }
            };

            // Initialize
            document.addEventListener('DOMContentLoaded', function() {
                const uploadArea = elements.uploadArea();
                const fileInput = elements.fileInput();

                // Theme compatibility
                const theme = localStorage.getItem('theme') || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                if (!document.documentElement.hasAttribute('data-theme')) {
                    document.documentElement.setAttribute('data-theme', theme);
                }

                // File input change
                fileInput.addEventListener('change', function() {
                    if (this.files.length > 0 && this.files[0].type.startsWith('image/')) {
                        elements.convertBtn().disabled = false;
                        ImageBase64Converter.showPreview(this.files[0]);
                    }
                });

                // Drag and drop
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    
                    const files = Array.from(e.dataTransfer.files).filter(file => 
                        file.type.startsWith('image/')
                    );
                    
                    if (files.length > 0) {
                        const dt = new DataTransfer();
                        dt.items.add(files[0]);
                        fileInput.files = dt.files;
                        
                        elements.convertBtn().disabled = false;
                        ImageBase64Converter.showPreview(files[0]);
                    }
                });

                // Click to upload
                uploadArea.addEventListener('click', () => fileInput.click());
            });
        })();
    </script>
</body>
</html>